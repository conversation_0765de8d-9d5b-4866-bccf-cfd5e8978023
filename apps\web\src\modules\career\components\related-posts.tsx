import {
  MainButton,
  SectionTitle,
} from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { Calendar1 } from 'lucide-react'

//---------------------------------------------------------------------------------
const __LIST_POST = [
  {
    title:
      'Office ipsum you must muted whistles previous sun chance cloud customer hurting skulls',
    image: '/images/image-branch.png',
    expect:
      "Office ipsum you must be muted. Build playing illustration decisions savvy functional hard algorithm solutions. Muted devil explore any reality. Look just these without give gave churning diligence. Third attached respectively solutions best. Well protocol race money businesses start boys working into. Charts finance feature catching pants don't.",
    category: 'category',
    created_at: '30 June 2025',
  },
  {
    title:
      'Office ipsum you must muted whistles previous sun chance cloud customer hurting skulls',
    image: '/images/image-branch.png',
    expect:
      "Office ipsum you must be muted. Build playing illustration decisions savvy functional hard algorithm solutions. Muted devil explore any reality. Look just these without give gave churning diligence. Third attached respectively solutions best. Well protocol race money businesses start boys working into. Charts finance feature catching pants don't.",
    category: 'category',
    created_at: '30 June 2025',
  },
  {
    title:
      'Office ipsum you must muted whistles previous sun chance cloud customer hurting skulls',
    image: '/images/image-branch.png',
    expect:
      "Office ipsum you must be muted. Build playing illustration decisions savvy functional hard algorithm solutions. Muted devil explore any reality. Look just these without give gave churning diligence. Third attached respectively solutions best. Well protocol race money businesses start boys working into. Charts finance feature catching pants don't.",
    category: 'category',
    created_at: '30 June 2025',
  },
]
//---------------------------------------------------------------------------------
export default function RelatedPosts() {
  return (
    <section className="relative z-10 bg-[#FFFCEE] py-10 md:py-20">
      <div
        className="bg-overlay absolute right-0 bottom-0 z-20"
        style={{
          backgroundImage: 'url(/images/bg-hexagon-right.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />
      <div
        className="absolute left-0 top-0 z-20 bg-cover bg-center"
        style={{ backgroundImage: 'url(/images/bg-hexagon-top.png)' }}
      />
      <div className="relative z-30 w-full container max-w-screen-xl mx-auto px-4">
        <div className="flex flex-col gap-10">
          <div className="flex items-center justify-between gap-4 ">
            <SectionTitle text="các sản phẩm liên quan" align="left" />
            <MainButton label="XEM THÊM" />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-10">
            {__LIST_POST.map((post: any, idx: number) => (
              <CardPost key={idx} post={post} />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

function CardPost({ post }: { post: any }) {
  return (
    <div
      className="flex flex-col border border-gray-200 rounded-xl overflow-hidden group bg-white"
      style={{
        boxShadow:
          '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
      }}
    >
      <div className="w-full h-auto aspect-[9/6] overflow-hidden ">
        <div
          className="w-full h-full group-hover:scale-110 ease-in-out transition-all duration-500"
          style={{
            backgroundImage: `url(${post?.image})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      </div>
      <div
        className="w-full h-[4px]"
        style={{
          background:
            'linear-gradient(90deg, #FC0 58.75%, #FFD326 83.64%, #FFDE59 100%)',
        }}
      />
      <div className="flex flex-col gap-4 p-6">
        <div className="flex flex-col gap-2">
          <Typography variant="h6" className="text-gray-800 line-clamp-2">
            {post?.title}
          </Typography>
          <Typography variant="body1" className="text-gray-700 line-clamp-3">
            {post?.expect}
          </Typography>
        </div>
        <div className="flex justify-between items-center">
          <div className="flex flex-col gap-2">
            <Typography
              variant="body1"
              className="text-[#FF9900] font-semibold"
            >
              {post?.category}
            </Typography>
            <div className="flex items-center gap-2">
              <Calendar1 className="text-[#AF0E0E] w-5 h-5" />
              <Typography variant="body2" className="text-[#AF0E0E] font-bold">
                {post?.created_at}
              </Typography>
            </div>
          </div>
          <MainButton />
        </div>
      </div>
    </div>
  )
}
