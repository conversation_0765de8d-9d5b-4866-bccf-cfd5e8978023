import { useRouter } from 'next/navigation'
import { useCallback } from 'react'
import { BUTTON_ACTIONS } from '../constants/survey.constants'
import { useSurveyContext } from '../context/survey-context'
import { StepConfig } from '../types/survey.types'
import { useSurveyValidation } from './use-survey-validation'

export interface SurveyNavigationHook {
  currentStep: StepConfig | null
  currentStepIndex: number
  totalSteps: number
  progress: number
  canGoNext: boolean
  canGoPrevious: boolean
  goToNextStep: () => void
  goToPreviousStep: () => void
  goToStep: (stepIndex: number) => void
  handleButtonAction: (action: string, url?: string) => void
  isFirstStep: boolean
  isLastStep: boolean
}

export function useSurveyNavigation(): SurveyNavigationHook {
  const router = useRouter()
  const {
    state,
    goToNextStep: contextGoNext,
    goToPreviousStep: contextGoPrevious,
    goToStep: contextGoToStep,
    getCurrentStep,
    getProgress,
    canGoNext: contextCanGoNext,
    canGoPrevious: contextCanGoPrevious,
    submitSurvey,
  } = useSurveyContext()

  const { validateCurrentStep } = useSurveyValidation()

  const currentStep = getCurrentStep()
  const currentStepIndex = state.currentStepIndex
  const totalSteps = state.config?.steps.length || 0
  const progress = getProgress()
  const canGoNext = contextCanGoNext()
  const canGoPrevious = contextCanGoPrevious()
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === totalSteps - 1

  const goToNextStep = useCallback(() => {
    if (!canGoNext) return

    // Validate current step before proceeding
    const validation = validateCurrentStep()
    if (validation.isValid) {
      contextGoNext()
      // Scroll to top when moving to next step
      window.scrollTo({ top: 0, behavior: 'smooth' })
    } else {
      // Scroll to first error question
      if (validation.firstErrorQuestionId) {
        const errorElement = document.getElementById(
          `question-${validation.firstErrorQuestionId}`,
        )
        if (errorElement) {
          errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
          // Add shake animation
          errorElement.classList.add('animate-shake')
          setTimeout(() => {
            errorElement.classList.remove('animate-shake')
          }, 600)
        }
      }
    }
  }, [canGoNext, validateCurrentStep, contextGoNext])

  const goToPreviousStep = useCallback(() => {
    if (!canGoPrevious) return
    contextGoPrevious()
    // Scroll to top when moving to previous step
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }, [canGoPrevious, contextGoPrevious])

  const goToStep = useCallback(
    (stepIndex: number) => {
      if (stepIndex < 0 || stepIndex >= totalSteps) return

      // If going forward, validate current step
      if (stepIndex > currentStepIndex) {
        const validation = validateCurrentStep()
        if (validation.isValid) {
          contextGoToStep(stepIndex)
          // Scroll to top when going to step
          window.scrollTo({ top: 0, behavior: 'smooth' })
        }
      } else {
        // Going backward is always allowed (if back navigation is enabled)
        contextGoToStep(stepIndex)
        // Scroll to top when going to step
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }
    },
    [currentStepIndex, totalSteps, validateCurrentStep, contextGoToStep],
  )

  const handleButtonAction = useCallback(
    async (action: string, url?: string) => {
      switch (action) {
        case BUTTON_ACTIONS.NEXT:
          goToNextStep()
          break

        case BUTTON_ACTIONS.PREVIOUS:
          goToPreviousStep()
          break

        case BUTTON_ACTIONS.SUBMIT: {
          const validation = validateCurrentStep(true) // Force validation with state update
          if (validation.isValid) {
            try {
              await submitSurvey()
              // Scroll to top when survey is completed
              window.scrollTo({ top: 0, behavior: 'smooth' })
              // Handle successful submission (could redirect or show success message)
              if (state.config?.settings.redirectOnComplete) {
                router.push(state.config.settings.redirectOnComplete)
              }
            } catch (error) {
              console.error('Survey submission failed:', error)
              // Handle submission error (show error message)
            }
          } else {
            // Scroll to first error question
            if (validation.firstErrorQuestionId) {
              const errorElement = document.getElementById(
                `question-${validation.firstErrorQuestionId}`,
              )
              if (errorElement) {
                errorElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                })
                // Add shake animation
                errorElement.classList.add('animate-shake')
                setTimeout(() => {
                  errorElement.classList.remove('animate-shake')
                }, 600)
              }
            }
          }
          break
        }

        case BUTTON_ACTIONS.CANCEL:
          // Could show confirmation dialog before canceling
          if (url) {
            router.push(url)
          } else {
            router.back()
          }
          break

        case BUTTON_ACTIONS.HOME:
          router.push(url || '/')
          break

        default:
          if (url) {
            router.push(url)
          }
          break
      }
    },
    [
      goToNextStep,
      goToPreviousStep,
      validateCurrentStep,
      submitSurvey,
      state.config,
      router,
    ],
  )

  return {
    currentStep,
    currentStepIndex,
    totalSteps,
    progress,
    canGoNext,
    canGoPrevious,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    handleButtonAction,
    isFirstStep,
    isLastStep,
  }
}
