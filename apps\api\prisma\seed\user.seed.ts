import { PrismaClient } from '@prisma/client'
import { hash } from 'argon2'

const prisma = new PrismaClient()

const users = [
  {
    email: '<EMAIL>',
    name: '<PERSON><PERSON><PERSON>',
  },
  {
    email: '<EMAIL>',
    name: 'Duong TT',
  },
]

export async function seedUsers() {
  const hashedPassword = await hash('********')
  for (const user of users) {
    const existingUser = await prisma.user.findUnique({
      where: { email: user.email },
    })

    if (existingUser) {
      console.log(`\t❌ User already exists: ${user.email}`)
      continue
    }

    const newUser = await prisma.user.create({
      data: {
        ...user,
        password: hashedPassword,
      },
    })

    await prisma.account.create({
      data: {
        user_id: newUser.id,
        provider: 'local',
        provider_account_id: newUser.id,
        type: 'credentials',
        token_type: 'Bearer',
      },
    })

    console.log(`\t✅ Seeded user: ${user.email}`)
  }
}
