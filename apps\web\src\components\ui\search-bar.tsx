'use client'

import { useEffect } from 'react'

import { useBoolean } from '@/libs/hooks/use-boolean'
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Input,
} from '@ttplatform/ui/components'
import { Command, Search } from 'lucide-react'

export function SearchBar() {
  const openDialog = useBoolean()

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
        event.preventDefault()
        openDialog.onTrue()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openDialog.onTrue])

  const isMac = /Mac|iPhone|iPad|iPod/i.test(navigator.userAgent)
  const modifierKey = isMac ? '⌘' : 'Ctrl'

  return (
    <div className="px-2">
      <div className="flex h-10 items-center gap-2 rounded-lg border border-gray-100 bg-white p-2 shadow-xs">
        <div className="relative flex-1">
          <Search className="absolute top-1/2 left-0 h-4 w-4 -translate-y-1/2 text-gray-800" />
          <Input
            type="text"
            placeholder="Search"
            className="h-8 rounded-md border-none bg-transparent pr-10 pl-6 text-sm focus-visible:ring-0 focus-visible:ring-offset-0"
            onClick={openDialog.onTrue}
          />
        </div>

        <Button
          variant="ghost"
          size="sm"
          className="flex h-7 items-center gap-1 rounded-md border border-gray-100 !px-1.5 text-gray-500 hover:bg-gray-100"
          onClick={openDialog.onTrue}
        >
          <Command className="h-4 w-4" />
          <span className="text-sm font-medium">F</span>
        </Button>
      </div>

      <Dialog open={openDialog.value} onOpenChange={openDialog.onFalse}>
        <DialogContent className="max-w-xl">
          <DialogHeader>
            <DialogTitle>Search</DialogTitle>
            <DialogDescription>
              Enter your search query below to find what you&apos;re looking
              for.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Input
              type="search"
              placeholder="Search..."
              className="pl-4"
              autoFocus
            />
            <div className="text-sm text-gray-500">
              Press <kbd>{modifierKey} + F</kbd> to open this dialog again.
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
