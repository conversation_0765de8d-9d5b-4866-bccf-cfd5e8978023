import { Skeleton } from '@ttplatform/ui/components'
import { AppContainer } from '@ttplatform/ui/templates'
import { Suspense } from 'react'
import { getSiteById } from '~/src/libs/data/cms-site'
import WebsiteDetail from '../organisms/website-detail'

type TProps = {
  params: Promise<{ id: string }>
}

export default async function TemplateWebsiteDetail({ params }: TProps) {
  const { id } = await params

  const site = await getSiteById(id)

  return (
    <AppContainer>
      <Suspense fallback={<Skeleton className="h-full w-full" />}>
        <WebsiteDetail details={site} />
      </Suspense>
    </AppContainer>
  )
}
