import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'

type TProps = {
  text: string
  active?: boolean
}

const SiteBadge = ({ text, active }: TProps) => {
  const bgColor = active ? 'bg-green-100' : 'bg-gray-100'
  const bgColorDot = active ? 'bg-green-800' : 'bg-gray-600'
  const textColor = active ? 'text-green-800' : 'text-gray-600'

  return (
    <div
      className={cn('px-2 py-1 rounded-sm flex items-center gap-1', bgColor)}
    >
      <div className={cn('w-1.5 h-1.5 rounded-full', bgColorDot)} />{' '}
      <Typography
        variant="small"
        className={cn('font-semibold text-[10px]', textColor)}
      >
        {text.toUpperCase()}
      </Typography>
    </div>
  )
}

export default SiteBadge
