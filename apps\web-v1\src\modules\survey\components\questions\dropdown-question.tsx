'use client'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
  Typography,
} from '@ttplatform/ui/components'
import { AnimatePresence, motion } from 'framer-motion'
import React, { useEffect, useRef, useState } from 'react'
import { useSurveyValidation } from '../../hooks/use-survey-validation'
import { DropdownQuestion as DropdownQuestionType } from '../../types/survey.types'

interface DropdownQuestionProps {
  question: DropdownQuestionType
  value: string
  onChange: (value: string) => void
  error?: string
}

export default function DropdownQuestion({
  question,
  value = '',
  onChange,
  error,
}: DropdownQuestionProps) {
  const { clearQuestionError } = useSurveyValidation()
  const [_isOpen, setIsOpen] = useState(false)
  const [otherValue, setOtherValue] = useState('')
  const [showOtherInput, setShowOtherInput] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const hasOtherOption = question.hasOtherOption
  const isOtherSelected = value.startsWith('other:')

  useEffect(() => {
    // Initialize other input if there's an existing other value
    if (isOtherSelected) {
      setShowOtherInput(true)
      setOtherValue(value.replace('other:', ''))
    }
  }, [value, isOtherSelected])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleOptionSelect = (optionValue: string) => {
    // Clear error immediately when user interacts
    if (error) {
      clearQuestionError(question.id)
    }

    if (optionValue === 'other') {
      setShowOtherInput(true)
      setIsOpen(false)
      if (!otherValue.trim()) {
        onChange('')
      } else {
        onChange(`other:${otherValue}`)
      }
    } else {
      setShowOtherInput(false)
      setOtherValue('')
      onChange(optionValue)
      setIsOpen(false)
    }
  }

  const handleOtherInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newOtherValue = e.target.value
    setOtherValue(newOtherValue)

    if (newOtherValue.trim()) {
      onChange(`other:${newOtherValue}`)
    } else {
      onChange('')
    }
  }

  const getDisplayValue = () => {
    if (!value) return 'Chọn một tùy chọn...'

    if (isOtherSelected) {
      return `Khác: ${value.replace('other:', '')}`
    }

    const option = question.options.find((opt) => opt.value === value)
    return option?.label || value
  }

  const _getSelectedOption = () => {
    if (isOtherSelected) return null
    return question.options.find((opt) => opt.value === value)
  }

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Dropdown */}
      <Select
        value={isOtherSelected ? 'other' : value}
        onValueChange={handleOptionSelect}
      >
        <SelectTrigger
          className={`cursor-pointer w-full bg-white !h-12 focus:ring-primary ${error ? 'border-destructive focus:ring-destructive' : ''}`}
        >
          <SelectValue placeholder="Select">
            <Typography variant="body2" className="text-foreground truncate">
              {getDisplayValue()}
            </Typography>
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="max-h-60 w-full">
          <SelectGroup>
            {question.options.map((option) => (
              <SelectItem
                key={option.id}
                value={option.value}
                className="focus:bg-primary/10 focus:text-primary"
              >
                <Typography variant="body2">{option.label}</Typography>
              </SelectItem>
            ))}

            {hasOtherOption && (
              <SelectItem
                value="other"
                className="focus:bg-primary/10 focus:text-primary border-t"
              >
                <Typography variant="body2">Khác...</Typography>
              </SelectItem>
            )}
          </SelectGroup>
        </SelectContent>
      </Select>

      {/* Other Input */}
      <AnimatePresence>
        {showOtherInput && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Typography variant="h6" className="font-semibold mb-4">
              Write your answer
            </Typography>
            <Textarea
              value={otherValue}
              onChange={(_e) => handleOtherInputChange}
              placeholder="Figma ipsum component variant main layer. Object vector content arrow frame follower."
              className="p-3 min-h-[100px] bg-white focus-visible:ring-primary border border-gray-200"
              rows={3}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
