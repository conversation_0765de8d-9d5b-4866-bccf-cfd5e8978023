import { Button, Input, Typography } from '@ttplatform/ui/components'
import { ArrowRightIcon } from 'lucide-react'
//-------------------------------------------------------------------
export default function ContactForm() {
  return (
    <div className="w-full p-6 bg-gray-50 rounded-xl ">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <Typography variant={'caption'} className="font-medium text-gray-600">
            Họ và tên
          </Typography>
          <Input placeholder="Họ và tên" />
        </div>
        <div className="flex flex-col gap-2">
          <Typography variant={'caption'} className="font-medium text-gray-600">
            Công ty
          </Typography>
          <Input placeholder="Họ và tên" />
        </div>
        <div className="flex flex-col gap-2">
          <Typography variant={'caption'} className="font-medium text-gray-600">
            Số điện thoại
          </Typography>
          <Input placeholder="Họ và tên" />
        </div>
        <div className="flex flex-col gap-2">
          <Typography variant={'caption'} className="font-medium text-gray-600">
            Email
          </Typography>
          <Input placeholder="Họ và tên" />
        </div>
        <div className="flex flex-col gap-2">
          <Typography variant={'caption'} className="font-medium text-gray-600">
            Nội dung yêu cầu
          </Typography>
          <Input placeholder="Họ và tên" />
        </div>
        <div className="flex justify-start mt-2">
          <Button>
            Gửi yêu cầu <ArrowRightIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
