'use client'
import { cn } from '@ttplatform/ui/lib'
import { LayoutGrid } from 'lucide-react'
import { useState } from 'react'

type ViewModeToggleProps = {
  value: string
  onChange: (value: string) => void
  iconSize?: number
  buttonSize?: number
}

export function ViewModeToggle({
  value,
  onChange,
  iconSize = 20,
  buttonSize = 20,
}: ViewModeToggleProps) {
  const [isHovered, setIsHovered] = useState<boolean>(false)
  return (
    <div className="flex gap-4">
      <button
        onClick={() => onChange('grid')}
        className={cn(
          'flex items-center justify-center rounded-md transition-colors cursor-pointer',
          value === 'grid'
            ? 'text-gray-800'
            : 'text-gray-500 hover:text-gray-700',
        )}
        style={{ width: buttonSize, height: buttonSize }}
      >
        <LayoutGrid size={iconSize} />
      </button>
      <button
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => onChange('table')}
        className={cn(
          'flex items-center justify-center rounded-md transition-colors cursor-pointer',
          value === 'table'
            ? 'text-gray-800'
            : 'text-gray-500 hover:text-gray-700',
        )}
        style={{ width: buttonSize, height: buttonSize }}
      >
        <IconColumns
          size={iconSize}
          color={value === 'table' || isHovered ? '#182230' : '#6a7282'}
        />
      </button>
    </div>
  )
}

const IconColumns = ({ color = '', size }: { color: string; size: number }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.8 3H6.2C5.0799 3 4.51984 3 4.09202 3.21799C3.71569 3.40973 3.40973 3.71569 3.21799 4.09202C3 4.51984 3 5.07989 3 6.2V17.8C3 18.9201 3 19.4802 3.21799 19.908C3.40973 20.2843 3.71569 20.5903 4.09202 20.782C4.51984 21 5.0799 21 6.2 21H6.8C7.9201 21 8.48016 21 8.90798 20.782C9.28431 20.5903 9.59027 20.2843 9.78201 19.908C10 19.4802 10 18.9201 10 17.8V6.2C10 5.0799 10 4.51984 9.78201 4.09202C9.59027 3.71569 9.28431 3.40973 8.90798 3.21799C8.48016 3 7.9201 3 6.8 3Z"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.8 3H17.2C16.0799 3 15.5198 3 15.092 3.21799C14.7157 3.40973 14.4097 3.71569 14.218 4.09202C14 4.51984 14 5.0799 14 6.2V17.8C14 18.9201 14 19.4802 14.218 19.908C14.4097 20.2843 14.7157 20.5903 15.092 20.782C15.5198 21 16.0799 21 17.2 21H17.8C18.9201 21 19.4802 21 19.908 20.782C20.2843 20.5903 20.5903 20.2843 20.782 19.908C21 19.4802 21 18.9201 21 17.8V6.2C21 5.0799 21 4.51984 20.782 4.09202C20.5903 3.71569 20.2843 3.40973 19.908 3.21799C19.4802 3 18.9201 3 17.8 3Z"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
