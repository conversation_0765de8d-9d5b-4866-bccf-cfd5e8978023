'use client'

import {
  <PERSON>ouse<PERSON><PERSON><PERSON><PERSON>,
  CarouselPrevious,
  Typography,
} from '@ttplatform/ui/components'

import { SectionTitle } from '@ttplatform/core-page-builder/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import { StarIcon } from 'lucide-react'
import { useRef } from 'react'
//---------------------------------------------------------------------------------
type IProps = {
  product: any
}
//---------------------------------------------------------------------------------
const __LIST = [
  {
    name: '<PERSON><PERSON><PERSON>',
    profession: 'Data Engineer, Sisyphus',
    content:
      'Office ipsum you must be muted. Sexy all innovation able launch. Better alpha crystallize light hill files horse. Forward activities decisions slipstream die day dear. Eye ensure welcome fastworks already give roll timepoint stands. Want language viral picture cta of. Assassin productize centric winning eco-system sorry an awareness buy-in. Dive customer create commitment design incentivization busy. Baked impact place meeting before race slipstream window. Site spaces masking here where recap marginalised corporate a reference. Procrastinating tiger ',
    rating: 5,
    avatar: '/images/products/avatar.jpg',
    image: '/images/products/may-cong-trinh.png',
  },
  {
    name: 'Caitlyn Queen',
    profession: 'Data Engineer, Sisyphus',
    content:
      'Office ipsum you must be muted. Sexy all innovation able launch. Better alpha crystallize light hill files horse. Forward activities decisions slipstream die day dear. Eye ensure welcome fastworks already give roll timepoint stands. Want language viral picture cta of. Assassin productize centric winning eco-system sorry an awareness buy-in. Dive customer create commitment design incentivization busy. Baked impact place meeting before race slipstream window. Site spaces masking here where recap marginalised corporate a reference. Procrastinating tiger ',
    rating: 5,
    avatar: '/images/products/avatar.jpg',
    image: '/images/products/may-cong-trinh.png',
  },
  {
    name: 'Caitlyn Prince',
    profession: 'Data Engineer, Sisyphus',
    content:
      'Office ipsum you must be muted. Sexy all innovation able launch. Better alpha crystallize light hill files horse. Forward activities decisions slipstream die day dear. Eye ensure welcome fastworks already give roll timepoint stands. Want language viral picture cta of. Assassin productize centric winning eco-system sorry an awareness buy-in. Dive customer create commitment design incentivization busy. Baked impact place meeting before race slipstream window. Site spaces masking here where recap marginalised corporate a reference. Procrastinating tiger ',
    rating: 5,
    avatar: '/images/products/avatar.jpg',
    image: '/images/products/may-cong-trinh.png',
  },
]
//---------------------------------------------------------------------------------
export default function DetailsReviews({ product }: IProps) {
  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))
  return (
    <section id="DANH_GIA" className="bg-gray-50">
      <div className="w-full container max-w-screen-xl mx-auto px-4">
        <div className="flex flex-col gap-10 py-10 lg:py-20">
          <SectionTitle
            text={`KHÁCH HÀNG ĐÁNH GIÁ VỀ ${product?.product_code}`}
            align="center"
          />
          <Carousel
            plugins={[plugin.current]}
            opts={{
              align: 'start',
              loop: true,
            }}
            className="w-full relative"
          >
            <CarouselContent>
              {__LIST.map((item: any, idx: number) => (
                <CarouselItem key={idx}>
                  <ReviewItem item={item} />
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="flex items-center gap-6 absolute z-50 bottom-[40px] right-[40px]">
              <CarouselPrevious
                className="w-12 h-11 relative top-0 left-0 translate-none bg-[#FC0] text-gray-800 hover:text-white border-transparent rounded-none hover:bg-[#FC0]"
                style={{
                  clipPath:
                    'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                  boxShadow:
                    '0px 30px 40px 0px rgba(0, 0, 0, 0.5), 0px 0px 20px 0px rgba(0, 0, 0, 0.5), 0px -10px 10px 0px rgba(255, 255, 255, 0.2)',
                }}
              />
              <CarouselNext
                className="w-12 h-11 relative top-0 right-0 translate-none bg-[#FC0] text-gray-800 hover:text-white border-transparent rounded-none hover:bg-[#FC0]"
                style={{
                  clipPath:
                    'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                  boxShadow:
                    '0px 30px 40px 0px rgba(0, 0, 0, 0.5), 0px 0px 20px 0px rgba(0, 0, 0, 0.5), 0px -10px 10px 0px rgba(255, 255, 255, 0.2)',
                }}
              />
            </div>
          </Carousel>
        </div>
      </div>
    </section>
  )
}
//---------------------------------------------------------------------------------
type IReviewItem = {
  item: any
}
function ReviewItem({ item }: IReviewItem) {
  return (
    <div
      className="grid grid-cols-1 md:grid-cols-2 w-full min-h-[400px] rounded-lg overflow-hidden bg-white"
      style={{
        boxShadow:
          '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
      }}
    >
      <div
        className="w-full h-full"
        style={{
          backgroundImage: `url(${item.image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />
      <div className="p-10 flex flex-col gap-10 justify-between">
        <div className="flex flex-col gap-y-6">
          <div className="flex items-center gap-1">
            {Array.from({ length: item.rating }).map((_, idx) => (
              <StarIcon key={idx} className="w-4 h-4 text-yellow-500" />
            ))}
          </div>
          <Typography variant="body1" className="text-gray-800">
            {item.content}
          </Typography>
        </div>
        <div className="flex items-center gap-4">
          <div
            className="w-14 h-auto aspect-[1.1/1]"
            style={{
              backgroundImage: `url(${item.avatar})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              clipPath:
                'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
            }}
          />
          <div className="flex flex-col">
            <Typography variant="body1" className="text-gray-800 font-semibold">
              {item.name}
            </Typography>
            <Typography variant="body2" className="text-gray-600">
              {item.profession}
            </Typography>
          </div>
        </div>
      </div>
    </div>
  )
}
