'use client'

import { use<PERSON><PERSON><PERSON>, useSearchParams } from 'next/navigation'
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
} from 'react'
import { ERROR_MESSAGES, STORAGE_KEYS } from '../constants/survey.constants'
import {
  StepConfig,
  SurveyAction,
  SurveyConfig,
  SurveyContextType,
  SurveyState,
} from '../types/survey.types'

// Initial State
const initialState: SurveyState = {
  config: null,
  currentStepIndex: 0,
  formData: {},
  isLoading: false,
  errors: {},
  isSubmitting: false,
  isCompleted: false,
  followUpQuestions: {},
}

// Survey Reducer
function surveyReducer(state: SurveyState, action: SurveyAction): SurveyState {
  switch (action.type) {
    case 'SET_CONFIG':
      return {
        ...state,
        config: action.payload,
        currentStepIndex: 0,
        formData: {},
        errors: {},
        isCompleted: false,
        followUpQuestions: {},
      }

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      }

    case 'NEXT_STEP':
      if (
        !state.config ||
        state.currentStepIndex >= state.config.steps.length - 1
      ) {
        return state
      }
      return {
        ...state,
        currentStepIndex: state.currentStepIndex + 1,
        errors: {}, // Clear errors when moving to next step
      }

    case 'PREVIOUS_STEP':
      if (state.currentStepIndex <= 0) {
        return state
      }
      return {
        ...state,
        currentStepIndex: state.currentStepIndex - 1,
        errors: {}, // Clear errors when moving to previous step
      }

    case 'GO_TO_STEP':
      if (
        !state.config ||
        action.payload < 0 ||
        action.payload >= state.config.steps.length
      ) {
        return state
      }
      return {
        ...state,
        currentStepIndex: action.payload,
        errors: {},
      }

    case 'UPDATE_FORM_DATA': {
      const newFormData = {
        ...state.formData,
        [action.payload.questionId]: action.payload.value,
      }

      // Auto-save to localStorage if enabled
      if (typeof window !== 'undefined') {
        localStorage.setItem(
          STORAGE_KEYS.SURVEY_DATA,
          JSON.stringify(newFormData),
        )
      }

      return {
        ...state,
        formData: newFormData,
      }
    }

    case 'SET_ERRORS':
      return {
        ...state,
        errors: action.payload,
      }

    case 'CLEAR_ERRORS':
      if (action.payload) {
        // Clear errors for specific step
        const newErrors = { ...state.errors }
        delete newErrors[action.payload]
        return {
          ...state,
          errors: newErrors,
        }
      }
      // Clear all errors
      return {
        ...state,
        errors: {},
      }

    case 'SET_SUBMITTING':
      return {
        ...state,
        isSubmitting: action.payload,
      }

    case 'SET_COMPLETED':
      return {
        ...state,
        isCompleted: action.payload,
      }

    case 'TOGGLE_FOLLOW_UP': {
      const newFollowUpQuestions = {
        ...state.followUpQuestions,
        [action.payload.questionId]: action.payload.show,
      }

      // Save follow-up questions to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem(
          STORAGE_KEYS.SURVEY_FOLLOW_UP,
          JSON.stringify(newFollowUpQuestions),
        )
      }

      return {
        ...state,
        followUpQuestions: newFollowUpQuestions,
      }
    }

    case 'RESET_SURVEY':
      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem(STORAGE_KEYS.SURVEY_DATA)
        localStorage.removeItem(STORAGE_KEYS.SURVEY_PROGRESS)
      }
      return {
        ...initialState,
        config: state.config, // Keep config but reset everything else
      }

    default:
      return state
  }
}

// Create Context
const SurveyContext = createContext<SurveyContextType | null>(null)

// Survey Provider Component
interface SurveyProviderProps {
  children: React.ReactNode
  initialConfig?: SurveyConfig
}

export function SurveyProvider({
  children,
  initialConfig,
}: SurveyProviderProps) {
  const [state, dispatch] = useReducer(surveyReducer, {
    ...initialState,
    config: initialConfig || null,
  })

  const router = useRouter()
  const searchParams = useSearchParams()

  // Load saved data from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedData = localStorage.getItem(STORAGE_KEYS.SURVEY_DATA)
      const savedProgress = localStorage.getItem(STORAGE_KEYS.SURVEY_PROGRESS)
      const savedFollowUp = localStorage.getItem(STORAGE_KEYS.SURVEY_FOLLOW_UP)

      if (savedData) {
        try {
          const formData = JSON.parse(savedData)
          Object.entries(formData).forEach(([questionId, value]) => {
            dispatch({
              type: 'UPDATE_FORM_DATA',
              payload: { questionId, value },
            })
          })
        } catch (error) {
          console.warn('Failed to load saved survey data:', error)
        }
      }

      if (savedProgress) {
        try {
          const stepIndex = Number.parseInt(savedProgress, 10)
          if (!Number.isNaN(stepIndex)) {
            dispatch({ type: 'GO_TO_STEP', payload: stepIndex })
          }
        } catch (error) {
          console.warn('Failed to load saved progress:', error)
        }
      }

      if (savedFollowUp) {
        try {
          const followUpQuestions = JSON.parse(savedFollowUp)
          Object.entries(followUpQuestions).forEach(([questionId, show]) => {
            dispatch({
              type: 'TOGGLE_FOLLOW_UP',
              payload: { questionId, show: Boolean(show) },
            })
          })
        } catch (error) {
          console.warn('Failed to load saved follow-up questions:', error)
        }
      }
    }
  }, [])

  // Load step from URL query parameter on mount
  useEffect(() => {
    const stepFromUrl = searchParams.get('step')
    const completedFromUrl = searchParams.get('completed')

    if (completedFromUrl === 'true') {
      dispatch({ type: 'SET_COMPLETED', payload: true })
    } else if (stepFromUrl && state.config) {
      const stepIndex = Number.parseInt(stepFromUrl, 10)
      if (
        !Number.isNaN(stepIndex) &&
        stepIndex >= 0 &&
        stepIndex < state.config.steps.length
      ) {
        dispatch({ type: 'GO_TO_STEP', payload: stepIndex })
      }
    }
  }, [searchParams, state.config])

  // Update URL when step changes or survey is completed
  useEffect(() => {
    if (typeof window !== 'undefined' && state.config) {
      const params = new URLSearchParams(window.location.search)

      if (state.isCompleted) {
        params.delete('step')
        params.set('completed', 'true')
      } else {
        params.delete('completed')
        params.set('step', state.currentStepIndex.toString())
      }

      const newUrl = `${window.location.pathname}?${params.toString()}`
      router.replace(newUrl, { scroll: false })
    }
  }, [state.currentStepIndex, state.isCompleted, state.config, router])

  // Save progress to localStorage when step changes
  useEffect(() => {
    if (typeof window !== 'undefined' && state.config) {
      localStorage.setItem(
        STORAGE_KEYS.SURVEY_PROGRESS,
        state.currentStepIndex.toString(),
      )
    }
  }, [state.currentStepIndex, state.config])

  // Helper Methods
  const goToNextStep = useCallback(() => {
    dispatch({ type: 'NEXT_STEP' })
  }, [])

  const goToPreviousStep = useCallback(() => {
    dispatch({ type: 'PREVIOUS_STEP' })
  }, [])

  const goToStep = useCallback((stepIndex: number) => {
    dispatch({ type: 'GO_TO_STEP', payload: stepIndex })
  }, [])

  const updateAnswer = useCallback((questionId: string, value: any) => {
    dispatch({
      type: 'UPDATE_FORM_DATA',
      payload: { questionId, value },
    })
  }, [])

  const validateCurrentStep = useCallback((): boolean => {
    if (!state.config) return false

    const currentStep = state.config.steps[state.currentStepIndex]
    if (!currentStep || !currentStep.questions) return true

    const stepErrors: { [questionId: string]: string } = {}
    let isValid = true

    currentStep.questions.forEach((question) => {
      const value = state.formData[question.id]

      // Check required fields
      if (question.required && (!value || value === '')) {
        stepErrors[question.id] = 'Trường này là bắt buộc'
        isValid = false
      }

      // Type-specific validation
      if (value) {
        switch (question.type) {
          case 'open-ended':
            if (question.minLength && value.length < question.minLength) {
              stepErrors[question.id] =
                `Vui lòng nhập ít nhất ${question.minLength} ký tự`
              isValid = false
            }
            if (question.maxLength && value.length > question.maxLength) {
              stepErrors[question.id] =
                `Vui lòng nhập không quá ${question.maxLength} ký tự`
              isValid = false
            }
            break
          case 'rating':
            if (
              typeof value !== 'number' ||
              value < question.minValue ||
              value > question.maxValue
            ) {
              stepErrors[question.id] = 'Vui lòng chọn mức độ đánh giá hợp lệ'
              isValid = false
            }
            break
        }
      }
    })

    if (!isValid) {
      dispatch({
        type: 'SET_ERRORS',
        payload: {
          ...state.errors,
          [currentStep.id]: stepErrors,
        },
      })
    } else {
      dispatch({ type: 'CLEAR_ERRORS', payload: currentStep.id })
    }

    return isValid
  }, [state.config, state.currentStepIndex, state.formData, state.errors])

  const submitSurvey = useCallback(async (): Promise<void> => {
    if (!state.config) {
      throw new Error(ERROR_MESSAGES.SURVEY_NOT_FOUND)
    }

    dispatch({ type: 'SET_SUBMITTING', payload: true })

    try {
      // Here you would implement the actual API call
      // For now, we'll simulate a submission
      await new Promise((resolve) => setTimeout(resolve, 1000))

      dispatch({ type: 'SET_COMPLETED', payload: true })

      // Clear saved data
      if (typeof window !== 'undefined') {
        localStorage.removeItem(STORAGE_KEYS.SURVEY_DATA)
        localStorage.removeItem(STORAGE_KEYS.SURVEY_PROGRESS)
      }
    } catch (error) {
      console.error('Survey submission failed:', error)
      throw new Error(ERROR_MESSAGES.SUBMISSION_FAILED)
    } finally {
      dispatch({ type: 'SET_SUBMITTING', payload: false })
    }
  }, [state.config])

  const resetSurvey = useCallback(() => {
    dispatch({ type: 'RESET_SURVEY' })
  }, [])

  const getCurrentStep = useCallback((): StepConfig | null => {
    if (!state.config) return null
    return state.config.steps[state.currentStepIndex] || null
  }, [state.config, state.currentStepIndex])

  const getProgress = useCallback((): number => {
    if (!state.config) return 0
    return ((state.currentStepIndex + 1) / state.config.steps.length) * 100
  }, [state.config, state.currentStepIndex])

  const canGoNext = useCallback((): boolean => {
    if (!state.config) return false
    return state.currentStepIndex < state.config.steps.length - 1
  }, [state.config, state.currentStepIndex])

  const canGoPrevious = useCallback((): boolean => {
    if (!state.config?.settings.allowBackNavigation) return false
    return state.currentStepIndex > 0
  }, [state.config, state.currentStepIndex])

  const contextValue: SurveyContextType = {
    state,
    dispatch,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    updateAnswer,
    validateCurrentStep,
    submitSurvey,
    resetSurvey,
    getCurrentStep,
    getProgress,
    canGoNext,
    canGoPrevious,
  }

  return (
    <SurveyContext.Provider value={contextValue}>
      {children}
    </SurveyContext.Provider>
  )
}

// Custom Hook to use Survey Context
export function useSurveyContext(): SurveyContextType {
  const context = useContext(SurveyContext)
  if (!context) {
    throw new Error('useSurveyContext must be used within a SurveyProvider')
  }
  return context
}
