// cms.prisma

/// ///////////////////
/// ///////////////////

enum PageStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  SCHEDULED
}

enum PageLayout {
  DEFAULT
  FULL_WIDTH
  BOXED
}

enum SiteStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  SCHEDULED
}

///////////////////////////
// 🧱 CMS
///////////////////////////

model CmsDomain {
  id         String    @id @default(uuid())
  name       String
  domain     String    @unique
  is_active  Boolean   @default(true)
  metadata   Json?
  created_by String?
  updated_by String?
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?
  sites      CmsSite[]

  user_id String
  user    User   @relation(fields: [user_id], references: [id])

  @@index([deleted_at])
  @@index([domain])
}

model CmsSite {
  id           String     @id @default(uuid())
  name         String
  status       SiteStatus @default(DRAFT)
  metadata     Json?
  published_at DateTime?
  is_primary   Boolean    @default(false)
  brand_kit    String[]
  created_by   String?
  updated_by   String?
  created_at   DateTime   @default(now())
  updated_at   DateTime   @updatedAt
  deleted_at   DateTime?

  user_id String
  user    User   @relation(fields: [user_id], references: [id])

  domain_id String?
  domain    CmsDomain? @relation(fields: [domain_id], references: [id])
  pages     CmsPage[]

  @@index([deleted_at])
}

model CmsPage {
  id              String       @id @default(uuid())
  title           String
  slug            String       @unique
  content         Json?
  status          PageStatus   @default(DRAFT)
  site_id         String
  site            CmsSite      @relation(fields: [site_id], references: [id], onDelete: Cascade)
  metadata        Json?
  is_default      Boolean      @default(false)
  is_hide_nav     Boolean      @default(false)
  is_hide_footer  Boolean      @default(false)
  is_hide_header  Boolean      @default(false)
  is_hide_sidebar Boolean      @default(false)
  page_layout     PageLayout   @default(DEFAULT)
  created_by      String?
  updated_by      String?
  published_at    DateTime?
  created_at      DateTime     @default(now())
  updated_at      DateTime     @updatedAt
  deleted_at      DateTime?
  sections        CmsSection[]

  @@index([deleted_at])
  @@index([metadata], type: Gin)
}

model CmsSection {
  id         String    @id @default(uuid())
  name       String
  fields     Json?
  rank       Int
  block_type String
  is_active  Boolean   @default(true)
  page_id    String
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?
  block_id   String
  block      CmsBlock  @relation(fields: [block_id], references: [id], onDelete: Cascade)
  page       CmsPage   @relation(fields: [page_id], references: [id], onDelete: Cascade)

  @@index([page_id])
  @@index([deleted_at])
}

model CmsBlock {
  id          String           @id @default(uuid())
  name        String
  icon        String?
  description String?
  category_id String
  is_active   Boolean          @default(true)
  rank        Int
  block_type  String
  deleted_at  DateTime?
  category    CmsBlockCategory @relation(fields: [category_id], references: [id], onDelete: Cascade)
  sections    CmsSection[]

  @@index([deleted_at])
}

model CmsBlockCategory {
  id          String     @id @default(uuid())
  name        String
  description String?
  rank        Int        @default(0)
  is_active   Boolean    @default(true)
  metadata    Json?
  created_at  DateTime   @default(now())
  updated_at  DateTime   @updatedAt
  deleted_at  DateTime?
  blocks      CmsBlock[]

  @@index([deleted_at])
  @@index([metadata], type: Gin)
}

model CmsMedia {
  id         String    @id @default(uuid())
  name       String
  type       String
  url        String
  size       Int
  metadata   Json?
  created_by String?
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?

  @@index([created_at])
  @@index([deleted_at])
  @@index([metadata], type: Gin)
}
