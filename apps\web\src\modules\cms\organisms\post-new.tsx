'use client'

import { Button, Typography } from '@ttplatform/ui/components'

export default function PostNew() {
  const handleAddNewPost = () => {
    console.log('TODO: Add new post')
  }

  const renderHeader = (
    <div className="flex flex-row justify-between w-full">
      <Typography variant="h3">Create new post</Typography>
      <Button
        color="info"
        variant="secondary"
        onClick={handleAddNewPost}
        size="lg"
      >
        + New Post
      </Button>
    </div>
  )

  const renderForm = (
    <div className="flex flex-col w-full">
      <div className="flex flex-row justify-between w-full">
        <Typography variant="h3">Create new post</Typography>
      </div>
    </div>
  )

  return (
    <>
      {renderHeader}

      {renderForm}
    </>
  )
}
