import { Module } from '@nestjs/common'
import { PrismaService } from '../persistence/prisma/prisma.service'
import { UiKitBlockGroupsController } from './ui-kit-block-groups.controller'
import { UiKitBlockGroupsService } from './ui-kit-block-groups.service'

@Module({
  controllers: [UiKitBlockGroupsController],
  providers: [UiKitBlockGroupsService, PrismaService],
})
export class UiKitBlockGroupsModule {}
