import { AnyField<PERSON><PERSON>, useForm } from '@tanstack/react-form'
import { I_SiteReturn } from '@ttplatform/common'
import { E_SiteStatus } from '@ttplatform/common/src/libs/enums/cms.enum'
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ttplatform/ui/components'
import { useMemo } from 'react'
import { toast } from 'sonner'
import { FieldInfo } from '~/src/components/tanstack-form'
import { createSite } from '~/src/libs/data/cms-site'

type TProps = {
  details?: I_SiteReturn
  title: string
  description?: string
  renderTrigger: React.ReactNode
}

export const SiteDialogForm = ({
  details,
  title,
  description,
  renderTrigger,
}: TProps) => {
  const defaultValues = useMemo(() => {
    return {
      name: details?.name || '',
      status: details?.status || E_SiteStatus.PUBLISHED,
    }
  }, [details])

  const form = useForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      await createSite(value)
      toast.success('Site created successfully')
    },
  })

  // const form = useForm({
  //   defaultValues,
  //   onSubmit: async ({ value }) => {
  //     // Do something with form data
  //     console.log(value);
  //   },
  // });

  const renderForm = (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        form.handleSubmit()
      }}
    >
      <div className="grid gap-4 py-4">
        <div className="flex flex-col gap-2">
          <form.Field name="name">
            {(field: AnyFieldApi) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Site</Label>
                <Input
                  id={field.name}
                  name={field.name}
                  value={field.state.value}
                  placeholder="Ex: The primary site"
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                />
                <FieldInfo field={field} />
              </div>
            )}
          </form.Field>
        </div>

        <form.Field name="status">
          {(field: AnyFieldApi) => (
            <div className="space-y-2">
              <Label htmlFor={field.name}>Status</Label>
              <Select>
                <SelectTrigger className="w-full" onChange={field.handleChange}>
                  <SelectValue placeholder="Select a status" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(E_SiteStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FieldInfo field={field} />
            </div>
          )}
        </form.Field>

        <form.Subscribe
          selector={(state) => [state.canSubmit, state.isSubmitting]}
        >
          {([canSubmit, isSubmitting]) => (
            <Button type="submit" disabled={!canSubmit} loading={isSubmitting}>
              {isSubmitting ? '...' : 'Submit'}
            </Button>
          )}
        </form.Subscribe>
      </div>
    </form>
  )

  return (
    <Dialog>
      {/* @ts-ignore */}
      <DialogTrigger asChild>{renderTrigger}</DialogTrigger>
      <DialogContent className="max-w-md w-full">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        {renderForm}
      </DialogContent>
    </Dialog>
  )
}
