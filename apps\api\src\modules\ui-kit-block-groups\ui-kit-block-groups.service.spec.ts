import { Test, TestingModule } from '@nestjs/testing'
import { UiKitBlockGroupsService } from './ui-kit-block-groups.service'

describe('UiKitBlockGroupsService', () => {
  let service: UiKitBlockGroupsService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UiKitBlockGroupsService],
    }).compile()

    service = module.get<UiKitBlockGroupsService>(UiKitBlockGroupsService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })
})
