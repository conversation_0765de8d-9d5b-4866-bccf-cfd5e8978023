import { Skeleton } from '@ttplatform/ui/components'
import React, { Suspense } from 'react'
import { DesignLayout } from '~/src/modules/site-builder/components'

interface LayoutProps {
  children: React.ReactNode
}

export default async function DesignPageLayout({ children }: LayoutProps) {
  return (
    <DesignLayout>
      <Suspense fallback={<Skeleton className="h-full w-full" />}>
        {children}
      </Suspense>
    </DesignLayout>
  )
}
