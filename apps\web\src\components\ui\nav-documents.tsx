'use client'

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@ttplatform/ui/components'
import CustomLink from '../custom-link'

type T_NavDocuments = {
  items: {
    title: string
    url: string
    icon?: React.ReactNode
  }[]
}

export function NavDocuments({ items }: T_NavDocuments) {
  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarMenu className="flex flex-col gap-2">
        {items.map((item, idx) => (
          <SidebarMenuItem key={idx}>
            <SidebarMenuButton asChild>
              <CustomLink href={item.url || '#'} className="flex items-center">
                {item.icon && item.icon}
                <span>{item.title}</span>
              </CustomLink>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}
