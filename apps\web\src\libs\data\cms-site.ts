'use server'

import { fetcher } from '@/utils/api'
import type {
  CreateSiteSchema,
  I_Paging,
  I_SiteListReturn,
  I_SiteReturn,
  UpdateSiteSchema,
} from '@ttplatform/common'
import { revalidateTag } from 'next/cache'
import { getAuthHeaders } from '~/src/utils/get-token'

////////////////////////////////////////////////////////////
// SITES
////////////////////////////////////////////////////////////

/**
 * Get list of sites
 * @queryParams
 *  - `page`: number - The page number to get
 *  - `perPage`: number - The number of sites to get per page
 *  - `sort`: string - The sort order to get the sites
 *  - `sortOrder`: string - The sort order to get the sites
 *  - `search`: string - The search query to get the sites
 *  - `status`: string - The status of the sites to get
 *
 * @path GET /sites
 * @returns I_SiteListReturn
 * **/
type I_SiteListParams = I_Paging & {
  query?: string
}
export async function getSiteList(
  params: I_SiteListParams = { page: '1', take: '10' },
  options?: {
    revalidate?: boolean
    tags?: string[]
  },
): Promise<I_SiteListReturn> {
  const headers = await getAuthHeaders()

  try {
    const res = await fetcher('/sites', {
      params: {
        ...params,
      },
      headers,
      options: {
        revalidate: options?.revalidate ?? false,
        tags: ['cms-sites'],
      },
    })

    return res as I_SiteListReturn
  } catch (error) {
    console.error('getSiteList error', error)
    if (error instanceof Error && error.message === 'Unauthorized') {
      window.location.href = '/error/401'
    }
    throw error
  }
}

/**
 * Get site by id
 * @path GET /sites/:id
 * @returns I_SiteReturn
 * **/
export async function getSiteById(id: string): Promise<I_SiteReturn> {
  try {
    const headers = {
      ...(await getAuthHeaders()),
    }

    const res = await fetcher(`/sites/${id}`, {
      headers,
      options: {
        revalidate: true,
        tags: [`cms-site-by-id-${id}`],
      },
    })

    return res.data as I_SiteReturn
  } catch (error) {
    console.error(error)
    throw error
  }
}

/**
 * Create site
 * @path POST /sites
 * @returns I_SiteReturn
 * **/
export async function createSite(
  site: CreateSiteSchema,
): Promise<I_SiteReturn> {
  try {
    const headers = {
      ...(await getAuthHeaders()),
    }

    const res = await fetcher('/sites', {
      method: 'POST',
      body: site,
      headers,
    })

    revalidateTag('cms-sites')
    return res.data as I_SiteReturn
  } catch (error) {
    console.error(error)
    throw error
  }
}

/**
 * Update site
 * @path PATCH /sites/:id
 * @returns I_SiteReturn
 * **/

export async function updateSite(
  id: string,
  site: UpdateSiteSchema,
): Promise<I_SiteReturn> {
  try {
    const headers = {
      ...(await getAuthHeaders()),
    }

    const res = await fetcher(`/sites/${id}`, {
      method: 'PATCH',
      body: site,
      headers,
    })

    revalidateTag('cms-sites')
    revalidateTag(`cms-site-by-id-${id}`)
    return res.data as I_SiteReturn
  } catch (error) {
    console.error(error)
    throw error
  }
}

/**
 * Delete site
 * @queryParams
 *  - `isSoft`: boolean - The soft delete flag
 *
 * @path DELETE /sites/:id
 * @returns I_SiteReturn
 * **/

export async function deleteSite(id: string, isSoft?: boolean): Promise<void> {
  try {
    const headers = {
      ...(await getAuthHeaders()),
    }

    await fetcher(`/sites/${id}`, {
      method: 'DELETE',
      headers,
      ...(isSoft !== undefined && { params: { isSoft: isSoft.toString() } }),
    })

    revalidateTag('cms-sites')
    revalidateTag(`cms-site-by-id-${id}`)

    return
  } catch (error) {
    console.error(error)
    throw error
  }
}
