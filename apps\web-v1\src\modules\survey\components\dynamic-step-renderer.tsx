'use client'

import { STEP_TYPES } from '../constants/survey.constants'
import { StepConfig } from '../types/survey.types'

import BaseStep from './base-step'
// Import step components
import SurveyConfirmationStep from './survey-confirmation-step'
import SurveyPolicyStep from './survey-policy-step'
import SurveyQuestionsStep from './survey-questions-step'

interface DynamicStepRendererProps {
  step: StepConfig
  className?: string
}

export default function DynamicStepRenderer({
  step,
  className = '',
}: DynamicStepRendererProps) {
  const renderStepContent = () => {
    switch (step.type) {
      case STEP_TYPES.POLICY:
        return <SurveyPolicyStep stepConfig={step} />

      case STEP_TYPES.CONFIRMATION:
        return <SurveyConfirmationStep stepConfig={step} />

      case STEP_TYPES.QUESTIONS:
        return <SurveyQuestionsStep stepConfig={step} />

      case STEP_TYPES.CUSTOM:
        // For custom steps, render with BaseStep and custom content
        return (
          <BaseStep step={step} className={className}>
            {step.content && (
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: step.content }}
              />
            )}
          </BaseStep>
        )

      default:
        console.warn(`Unknown step type: ${step.type}`)
        return (
          <BaseStep step={step} className={className}>
            <div className="p-4 border border-destructive/30 rounded bg-destructive/5">
              <Typography variant="body2" className="text-destructive">
                Unsupported step type: {step.type}
              </Typography>
              <Typography
                variant="caption"
                className="text-destructive/70 mt-2 block"
              >
                Step ID: {step.id}
              </Typography>
            </div>
          </BaseStep>
        )
    }
  }

  return (
    <div key={step.id} className={className}>
      {renderStepContent()}
    </div>
  )
}

// Multi-step renderer with animation
interface MultiStepRendererProps {
  steps: StepConfig[]
  currentStepIndex: number
  className?: string
}

export function MultiStepRenderer({
  steps,
  currentStepIndex,
  className = '',
}: MultiStepRendererProps) {
  const currentStep = steps[currentStepIndex]

  if (!currentStep) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-3">
          <Typography variant="h4" className="text-foreground">
            Không tìm thấy bước này
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            Bước {currentStepIndex + 1} không tồn tại trong khảo sát này.
          </Typography>
        </div>
      </div>
    )
  }

  return (
    <DynamicStepRenderer
      key={currentStep.id}
      step={currentStep}
      className={className}
    />
  )
}
