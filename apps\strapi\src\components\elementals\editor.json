{"collectionName": "components_elementals_editors", "info": {"displayName": "Editor"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "ckeidtor": {"type": "customField", "customField": "plugin::ckeditor.CKEditor", "options": {"output": "HTML", "preset": "rich", "licenseKey": "eyJhbGciOiJFUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.DMOArIydvMbb7FAHz3rR7_52QrG2i15MkqBhASZma63OAOsRQuUQhN-a5PFpMgOkpBMk85wXpcUzTZzJcCNUFg"}}, "editor": {"type": "blocks"}}, "config": {}}