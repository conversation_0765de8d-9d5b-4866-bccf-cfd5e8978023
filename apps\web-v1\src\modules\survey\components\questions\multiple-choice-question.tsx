'use client'

import {
  Checkbox,
  Label,
  RadioGroup,
  RadioGroupItem,
  Textarea,
  Typography,
} from '@ttplatform/ui/components'
import { AnimatePresence, motion } from 'framer-motion'
import React, { useState } from 'react'
import { useSurveyValidation } from '../../hooks/use-survey-validation'
import { MultipleChoiceQuestion as MultipleChoiceQuestionType } from '../../types/survey.types'

interface MultipleChoiceQuestionProps {
  question: MultipleChoiceQuestionType
  value: string | string[]
  onChange: (value: string | string[]) => void
  error?: string
}

export default function MultipleChoiceQuestion({
  question,
  value = question.allowMultiple ? [] : '',
  onChange,
  error,
}: MultipleChoiceQuestionProps) {
  const { clearQuestionError } = useSurveyValidation()
  const [otherValue, setOtherValue] = useState('')
  const [showOtherInput, setShowOtherInput] = useState(false)

  const isMultiple = question.allowMultiple
  const selectedValues = Array.isArray(value) ? value : [value].filter(Boolean)
  const hasOtherOption = question.hasOtherOption

  const handleOptionChange = (optionValue: string, checked: boolean) => {
    // Clear error immediately when user interacts
    if (error) {
      clearQuestionError(question.id)
    }

    if (optionValue === 'other') {
      setShowOtherInput(checked)
      if (!checked) {
        setOtherValue('')
        // Remove other value from selection
        if (isMultiple) {
          const newValues = selectedValues.filter(
            (v) => !v.startsWith('other:'),
          )
          onChange(newValues)
        } else {
          onChange('')
        }
      }
      return
    }

    if (isMultiple) {
      const currentValues = selectedValues.filter(
        (v) => !v.startsWith('other:'),
      )
      if (checked) {
        onChange([...currentValues, optionValue])
      } else {
        onChange(currentValues.filter((v) => v !== optionValue))
      }
    } else {
      onChange(checked ? optionValue : '')
    }
  }

  const handleOtherInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newOtherValue = e.target.value
    setOtherValue(newOtherValue)

    if (newOtherValue.trim()) {
      const otherOptionValue = `other:${newOtherValue}`
      if (isMultiple) {
        const currentValues = selectedValues.filter(
          (v) => !v.startsWith('other:'),
        )
        onChange([...currentValues, otherOptionValue])
      } else {
        onChange(otherOptionValue)
      }
    } else {
      // Remove other value if input is empty
      if (isMultiple) {
        const newValues = selectedValues.filter((v) => !v.startsWith('other:'))
        onChange(newValues)
      } else {
        onChange('')
      }
    }
  }

  const isOptionSelected = (optionValue: string) => {
    if (optionValue === 'other') {
      return selectedValues.some((v) => v.startsWith('other:'))
    }
    return selectedValues.includes(optionValue)
  }

  React.useEffect(() => {
    // Initialize other input if there's an existing other value
    const otherSelected = selectedValues.find((v) => v.startsWith('other:'))
    if (otherSelected) {
      setShowOtherInput(true)
      setOtherValue(otherSelected.replace('other:', ''))
    }
  }, [selectedValues])

  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {isMultiple ? (
        // Multiple choice with checkboxes
        <div className="flex gap-8 xl:gap-16 flex-wrap">
          {question.options.map((option, index) => (
            <div key={index} className="space-x-4 flex items-center">
              <Checkbox
                id={`${question.id}-${option.id}`}
                checked={isOptionSelected(option.value)}
                onCheckedChange={(checked) =>
                  handleOptionChange(option.value, !!checked)
                }
                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
              />
              <Label
                htmlFor={`${question.id}-${option.id}`}
                className="cursor-pointer"
              >
                <Typography variant="body2" className="text-foreground">
                  {option.label}
                </Typography>
              </Label>
            </div>
          ))}
        </div>
      ) : (
        // Single choice with radio buttons
        <RadioGroup
          value={selectedValues[0] || ''}
          onValueChange={(value) => handleOptionChange(value, true)}
          className="flex gap-8 xl:gap-16 flex-wrap"
        >
          {question.options.map((option, index) => (
            <motion.div
              key={index}
              className="flex items-center space-x-3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <RadioGroupItem
                value={option.value}
                id={`${question.id}-${option.id}`}
                className="text-primary"
              />
              <Label
                htmlFor={`${question.id}-${option.id}`}
                className="cursor-pointer"
              >
                <Typography variant="body2" className="text-foreground">
                  {option.label}
                </Typography>
              </Label>
            </motion.div>
          ))}
        </RadioGroup>
      )}

      {/* Other Option */}
      {hasOtherOption && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: question.options.length * 0.1 }}
          className="space-y-3"
        >
          {isMultiple ? (
            <div className="flex items-center space-x-3">
              <Checkbox
                id={`${question.id}-other`}
                checked={showOtherInput}
                onCheckedChange={(checked) =>
                  handleOptionChange('other', !!checked)
                }
                className="cursor-pointer data-[state=checked]:bg-primary data-[state=checked]:border-primary"
              />
              <Label
                htmlFor={`${question.id}-other`}
                className="cursor-pointer"
              >
                <Typography variant="body2" className="text-foreground">
                  Khác
                </Typography>
              </Label>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <RadioGroupItem
                value="other"
                id={`${question.id}-other`}
                className="text-primary cursor-pointer"
                onClick={() => handleOptionChange('other', true)}
              />
              <Label
                htmlFor={`${question.id}-other`}
                className="cursor-pointer"
              >
                <Typography variant="body2" className="text-foreground">
                  Khác
                </Typography>
              </Label>
            </div>
          )}

          <AnimatePresence>
            {showOtherInput && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Typography variant="h6" className="font-semibold mb-4">
                  Write your answer
                </Typography>
                <Textarea
                  value={otherValue}
                  onChange={(_e) => handleOtherInputChange}
                  placeholder="Figma ipsum component variant main layer. Object vector content arrow frame follower."
                  className="p-3 bg-white focus-visible:ring-primary border border-gray-200 min-h-[100px]"
                  rows={3}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </motion.div>
  )
}
