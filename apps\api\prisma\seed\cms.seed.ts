import { faker } from '@faker-js/faker'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

//////////////////////
// 🌱 CMS Domain
//////////////////////
export async function seedDomain() {
  console.log('\t🌱 Seeding domain...')

  const existingDomain = await prisma.cmsDomain.findFirst()
  if (existingDomain) {
    console.log(`\t❌ Domain already exists: ${existingDomain.name}`)
    return
  }

  const user = await prisma.user.findFirst()
  if (!user) {
    console.log('\t❌ User not found')
    return
  }

  const domain = await prisma.cmsDomain.create({
    data: {
      name: faker.company.name(),
      domain: faker.internet.domainName(),
      metadata: {},
      user_id: user.id,
      sites: {
        create: [
          {
            name: faker.company.name(),
            user_id: user.id,
            created_by: 'admin',
            status: 'PUBLISHED',
            metadata: {},
            pages: {
              create: [
                {
                  title: 'Home',
                  slug: 'home',
                  is_default: true,
                  status: 'PUBLISHED',
                  page_layout: 'DEFAULT',
                  content: { blocks: [] },
                  metadata: {},
                },
                {
                  title: 'About',
                  slug: 'about',
                  status: 'DRAFT',
                  page_layout: 'FULL_WIDTH',
                  content: { blocks: [] },
                  metadata: {},
                },
              ],
            },
          },
        ],
      },
    },
    include: { sites: { include: { pages: true } } },
  })

  console.log(`\t✅ Created domain: ${domain.name}\n`)
}

//////////////////////
// 🌱 CMS BLOCKS
//////////////////////
export async function seedBlocks() {
  console.log('\t🌱 Seeding blocks...')

  const existingCategory = await prisma.cmsBlockCategory.findFirst()
  if (existingCategory) {
    console.log(`\t❌ Block category already exists: ${existingCategory.name}`)
    return
  }

  const category = await prisma.cmsBlockCategory.create({
    data: {
      name: 'Text Blocks',
      description: 'Reusable text-based blocks',
      rank: 1,
      metadata: {},
    },
  })

  const block = await prisma.cmsBlock.create({
    data: {
      name: 'Hero Block',
      icon: 'star',
      description: 'A large banner block with image & text',
      block_type: 'hero',
      category_id: category.id,
      rank: 1,
    },
  })

  const pages = await prisma.cmsPage.findMany()

  for (const page of pages) {
    await prisma.cmsSection.create({
      data: {
        name: 'Hero Section',
        rank: 1,
        block_type: 'hero',
        fields: {
          title: faker.lorem.sentence(),
          subtitle: faker.lorem.paragraph(),
          image: faker.image.url(),
        },
        page_id: page.id,
        block_id: block.id,
      },
    })
  }

  console.log(`\t✅ Created blocks and sections for ${pages.length} pages\n`)
}

//////////////////////
// 🌱 CMS MEDIA
//////////////////////
export async function seedMedia() {
  console.log('\t🌱 Seeding media...')

  const existingMedia = await prisma.cmsMedia.findFirst()
  if (existingMedia) {
    console.log(`\t❌ Media already exists: ${existingMedia.name}`)
    return
  }
  for (let i = 0; i < 5; i++) {
    await prisma.cmsMedia.create({
      data: {
        name: faker.system.fileName(),
        type: 'image/png',
        url: faker.image.url(),
        size: faker.number.int({ min: 1000, max: 5000 }),
        metadata: { alt: faker.lorem.words(3) },
        created_by: 'admin',
      },
    })
  }
  console.log('\t✅ Seeded media files\n')
}
