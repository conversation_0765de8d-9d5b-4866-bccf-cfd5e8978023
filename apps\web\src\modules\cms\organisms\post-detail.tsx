'use client'

import { Button, Typography } from '@ttplatform/ui/components'
import { AppContainer } from '@ttplatform/ui/templates'

export default function WebsiteList() {
  const handleAddNewSite = () => {
    console.log('TODO: Add new site')
  }

  const renderHeader = (
    <div className="flex flex-row justify-between w-full">
      <Typography variant="h3">Your Websites</Typography>
      <Button
        color="info"
        variant="secondary"
        onClick={handleAddNewSite}
        size="lg"
      >
        + New Website
      </Button>
    </div>
  )

  const renderDetails = (
    <div className="flex flex-col w-full">
      <div className="flex flex-row justify-between w-full">
        <Typography variant="h3">Website details</Typography>
      </div>
    </div>
  )

  return (
    <AppContainer>
      {renderHeader}

      {renderDetails}
    </AppContainer>
  )
}
