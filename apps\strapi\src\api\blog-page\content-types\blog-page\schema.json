{"kind": "singleType", "collectionName": "blog_pages", "info": {"singularName": "blog-page", "pluralName": "blog-pages", "displayName": "/news", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.related-products", "dynamic-zone.related-articles", "dynamic-zone.features", "dynamic-zone.features-section", "dynamic-zone.news-featured-section", "dynamic-zone.news-listing-section", "dynamic-zone.page-hero-section", "dynamic-zone.widget", "dynamic-zone.media-text-section"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "banner_ads": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "elementals.banner-ads", "repeatable": true}}}