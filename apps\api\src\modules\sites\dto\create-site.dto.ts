import { Type } from 'class-transformer'
import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import { CreatePageDto } from 'src/modules/pages/dto/create-page.dto'

export enum WebsiteStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
}

export class CreateSiteDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  user_id: string

  @IsOptional()
  @IsString()
  domain_id: string

  @IsOptional()
  @IsBoolean()
  is_primary?: boolean

  @IsOptional()
  @IsBoolean()
  is_hide_nav?: boolean

  @IsOptional()
  @IsBoolean()
  is_hide_footer?: boolean

  @IsOptional()
  @IsBoolean()
  is_hide_header?: boolean

  @IsOptional()
  @IsBoolean()
  is_hide_sidebar?: boolean

  @IsOptional()
  @IsString()
  created_by?: string

  @IsOptional()
  @IsString()
  updated_by?: string

  @IsOptional()
  status?: WebsiteStatus

  @IsOptional()
  metadata?: any

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePageDto)
  pages?: CreatePageDto[] // Changed from null to optional array
}
