{"kind": "singleType", "collectionName": "globals", "info": {"singularName": "global", "pluralName": "globals", "displayName": "Global", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"header": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "global.header", "repeatable": false}, "footer": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "global.footer", "repeatable": false}, "navbar": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "global.navbar", "repeatable": false}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "dynamic_zone_before_footer": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.features-section", "dynamic-zone.form-subscribe"]}}}