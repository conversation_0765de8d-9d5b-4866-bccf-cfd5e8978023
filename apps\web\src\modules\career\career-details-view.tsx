import HRInfo from './components/HR-info'
import DetailsHeader from './components/details-header'
import RecruitmentForm from './components/recruitment-form'

// ----------------------------------------------------------------------
const _HEADING = {
  title: 'KỸ SƯ DỊCH VỤ MÁY PHÁT ĐIỆN – CƠ KHÍ',
  buttons: [],
}

const _BREADCRUMB = [
  {
    title: 'Trang chủ',
    href: '/',
  },
  {
    title: 'Tuyển dụng',
    href: '/career',
  },
  {
    title: '<PERSON><PERSON> sư dịch vụ máy phát điện - cơ khí',
    href: '/career/kỹ-sư-dịch-vụ-máy-phát-điện-cơ-khí',
  },
]
const _CURRENT_JOB = {
  title: 'KỸ SƯ DỊCH VỤ MÁY PHÁT ĐIỆN – CƠ KHÍ',
  slug: 'ky-su-dich-vu-may-phat-dien-co-khi',
  category: {
    id: 1,
    slug: 'bo-phan-ky-thuat-e-t',
    name: 'bộ phận kỹ thuật E&T',
  },
  workingForm: 'full-time',
  position: 'Bộ phận kỹ thuật E&T',
  location: {
    id: 1,
    slug: 'ha-noi',
    name: 'Hà Nội',
  },
  quantity: '4',
  deadline: '30 June 2025',
}
// ----------------------------------------------------------------------
export default function CareerDetailsView() {
  return (
    <>
      <DetailsHeader
        heading={_HEADING}
        BREADCRUMB={_BREADCRUMB}
        currentJob={_CURRENT_JOB}
      />
      <div className="w-full container max-w-screen-xl mx-auto px-4 pt-6 pb-10 md:pb-20">
        <div className="grid grid-cols-12 gap-x-10">
          <div className="col-span-8">content</div>
          <div className="col-span-4">
            <div className="flex flex-col gap-y-6">
              <HRInfo />
              <RecruitmentForm />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
