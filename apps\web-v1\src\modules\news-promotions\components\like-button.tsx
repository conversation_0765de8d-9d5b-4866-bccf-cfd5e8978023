'use client'
import { ThumbsUp } from 'lucide-react'
import { useState } from 'react'

interface LikeButtonProps {
  initialCount?: number
}

const LikeButton = ({ initialCount = 0 }: LikeButtonProps) => {
  const [count, setCount] = useState(initialCount)
  const [liked, setLiked] = useState(false)

  const handleClick = () => {
    if (liked) {
      setCount(count - 1)
    } else {
      setCount(count + 1)
    }
    setLiked(!liked)
  }

  return (
    <button
      className="flex items-center gap-1 text-yellow-500 hover:text-yellow-500 transition-colors cursor-pointer"
      onClick={handleClick}
    >
      <ThumbsUp size={16} className={liked ? 'text-yellow-500' : ''} />
      <span className={`text-sm ${liked ? 'text-yellow-500' : ''}`}>
        {count}
      </span>
    </button>
  )
}

export default LikeButton
