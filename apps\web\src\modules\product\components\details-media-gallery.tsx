import { SectionTitle } from '@ttplatform/core-page-builder/components'
import {
  Button,
  Carousel,
  CarouselContent,
  CarouselItem,
  Tabs,
  TabsList,
  TabsTrigger,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import Autoplay from 'embla-carousel-autoplay'
import { Download } from 'lucide-react'
import Image from 'next/image'
import { useRef, useState } from 'react'
import { HexagonIconButton } from '../../../../../../packages/core/page-builder/src/components/ui/custom-buttons/hexagon-button'
//---------------------------------------------------------------------------------
const _TABS_OPTIONS = [
  {
    label: 'HÌNH ẢNH',
    value: 'image',
  },
  {
    label: 'VIDEO',
    value: 'video',
  },
  {
    label: '360 VIEW',
    value: 'view',
  },
  {
    label: 'TÀI LIỆU',
    value: 'document',
  },
]

const _CONTENT_IMAGE = [
  '/images/products/related-1.png',
  '/images/products/related-2.png',
  '/images/products/related-3.png',
]
const _CONTENT_DOCUMENT = [
  'document-1.pdf',
  'document-2.pdf',
  'document-3.pdf',
  'document-4.pdf',
  'document-5.pdf',
]

const _VIEW_OPTIONS = [
  {
    label: 'View 360 từ bên ngoài',
    value: 'view-360-from-outside',
    image: '/images/products/may-cong-trinh.png',
  },
  {
    label: 'View 360 từ trong cabin',
    value: 'view-360-from-inside',
    image: '/images/products/view-360-inside.png',
  },
]

//---------------------------------------------------------------------------------
export function DetailsMediaGallery() {
  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  const [tabSelect, setTabSelect] = useState('image')
  const [viewSelect, setViewSelect] = useState(_VIEW_OPTIONS?.[0])
  const handleChangeTab = (value: string) => {
    setTabSelect(value)
  }

  const renderContentImage = (
    <Carousel
      plugins={[plugin.current]}
      opts={{
        align: 'start',
        loop: true,
      }}
      className="w-full relative"
    >
      <CarouselContent>
        {_CONTENT_IMAGE.map((item: any, idx: number) => (
          <CarouselItem key={idx} className="pl-4 md:pl-6 basis-3/4">
            <Image
              src={item}
              alt={item}
              width={1000}
              height={1000}
              className="w-full h-auto aspect-[9/6] object-cover rounded-2xl"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  )
  const renderContentVideo = (
    <div>
      <video
        controls
        playsInline
        // autoPlay
        muted
        loop
        preload="metadata"
        // poster={'videoSelect?.image'}
        style={{
          height: 'auto',
          width: '100%',
          aspectRatio: '16/9',
          objectFit: 'cover',
          borderRadius: '16px',
        }}
      >
        <source
          rel="preload"
          src={
            'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
          }
          type={'video/mp4'}
        />
      </video>
    </div>
  )
  const renderContentView = (
    <div className="w-full grid grid-cols-12 gap-10">
      <div className="col-span-3 ">
        <div className="flex flex-col gap-4 p-6 rounded-2xl bg-white">
          <Typography variant={'h6'} className="text-gray-800 uppercase">
            view 360
          </Typography>
          <div className="flex flex-col">
            {_VIEW_OPTIONS.map((option) => (
              <div
                key={option?.value}
                className="w-full cursor-pointer hover:bg-gray-100 p-2 rounded-lg"
                onClick={() => setViewSelect(option)}
              >
                <Typography
                  variant={'body1'}
                  className={cn(
                    'text-gray-800',
                    viewSelect?.value === option?.value && 'font-semibold',
                  )}
                >
                  {option?.label}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="col-span-9">
        <div
          className="w-full h-auto aspect-[4/3] object-cover rounded-2xl"
          style={{
            backgroundImage: `url(${viewSelect?.image})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        ></div>
      </div>
    </div>
  )
  const renderContentDocument = (
    <div className="w-full max-w-screen-lg mx-auto h-auto flex flex-col divide-y divide-white/20">
      {_CONTENT_DOCUMENT.map((item: any, idx: number) => (
        <div key={idx} className="flex gap-5 justify-between py-10">
          <Typography variant={'body1'} className="text-white">
            {item}
          </Typography>
          <HexagonIconButton icon={<Download />} variant="default" />
        </div>
      ))}
    </div>
  )
  return (
    <section id="GALLERY" className="relative bg-[#222] z-10">
      <div className="relative z-30 w-full container max-w-screen-xl mx-auto px-4">
        <div className="flex flex-col gap-10 py-10 lg:py-20">
          <div className="w-full h-auto flex items-center justify-between">
            <SectionTitle
              text="media gallery"
              align="left"
              className="text-white"
            />
            <div className="flex items-center gap-6">
              <Button>Gửi qua Zalo</Button>
              <Button>Tải về</Button>
            </div>
          </div>
          <Tabs
            value={tabSelect}
            onValueChange={handleChangeTab}
            className="w-full max-w-screen-lg mx-auto"
          >
            <TabsList className="w-full h-auto grid grid-cols-4 bg-transparent mx-auto gap-4">
              {_TABS_OPTIONS.map((option) => (
                <TabsTrigger
                  key={option?.value}
                  value={option?.value}
                  className="w-full py-2 px-3 text-white bg-[#313131] data-[state=active]:text-gray-800 data-[state=active]:bg-[#FFCC00]  border-b-2 border-b-transparent cursor-pointer rounded-full"
                >
                  <Typography variant={'body2'}>{option?.label}</Typography>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
          {/* content */}
          <div className="w-full max-h-[800px]">
            {tabSelect === 'image' && renderContentImage}
            {tabSelect === 'video' && renderContentVideo}
            {tabSelect === 'view' && renderContentView}
            {tabSelect === 'document' && renderContentDocument}
          </div>
        </div>
      </div>
    </section>
  )
}
