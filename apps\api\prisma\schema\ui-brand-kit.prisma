// ui-brand-kit.prisma
/**
 * UI Brand Kit
 * UI Brand Kit is a collection of assets that are used to create a brand.
 * It includes logos, colors, fonts, and other branding elements.
 */

//////////////////////////////////
//  UI BRAND KIT
//////////////////////////////////

model UiBrandKit {
  id          String    @id @default(uuid())
  name        String
  description String?
  colors      String[]
  trending    Boolean   @default(false)
  default     Boolean   @default(false)
  metadata    Json?
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  deleted_at  DateTime?
}
