'use server'

import { fetcher } from '@/utils/api'

// GET /auth/me
export async function getAccount() {
  try {
    const user = await fetcher('/auth/me')
    return user
  } catch (error) {
    console.error(error)
    throw error
  }
}

// GET /auth/signin
export async function login({
  email,
  password,
}: { email: string; password: string }) {
  try {
    const user = await fetcher('/auth/signin', {
      method: 'POST',
      body: { email, password },
    })
    return user
  } catch (error) {
    console.error('🚀 ~ signIn:', error)
    throw error
  }
}

export type TToken = {
  access_token: string
  refresh_token: string
}

export async function getRefreshToken(refreshToken: string): Promise<any> {
  try {
    const res = (await fetcher('/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${refreshToken}`,
      },
    })) as any

    if (res.error) {
      const statusCode = res.statusCode
      if (statusCode == 401) {
        return {
          error: 'RefreshAccessTokenError',
        }
      }
    }

    return res as TToken
  } catch (error) {
    console.error(error)
    // throw error;
    return {
      error: 'RefreshAccessTokenError',
    }
  }
}

// POST /auth/signup
export async function register({
  email,
  password,
  name,
  image,
}: { name: string; email: string; password: string; image?: string }) {
  try {
    const user = await fetcher('/auth/signup', {
      method: 'POST',
      body: { email, password, name, image },
    })

    return user
  } catch (error) {
    console.error(error)
    throw error
  }
}

// POST /auth/signout
export async function logout() {
  try {
    await fetcher('/auth/signout', {
      method: 'DELETE',
    })
  } catch (error) {
    console.error(error)
    throw error
  }
}
