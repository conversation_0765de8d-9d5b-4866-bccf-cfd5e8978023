'use server'

import { cookies } from 'next/headers'
import { KEYS } from '~/src/config-global'

const { keyAccessToken, keyRefreshToken, keyCacheId } = KEYS

export const getAuthHeaders = async (): Promise<
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  { authorization: string } | {}
> => {
  const nextCookies = await cookies()
  const token = nextCookies.get(keyAccessToken)?.value

  if (token) {
    return { authorization: `Bearer ${token}` }
  }

  return {}
}

export const getAuthRefreshToken = async (): Promise<string | undefined> => {
  const nextCookies = await cookies()
  return nextCookies.get(keyRefreshToken)?.value
}

export const getCacheTag = async (tag: string): Promise<string> => {
  try {
    const nextCookies = await cookies()
    const cacheId = nextCookies.get(keyCacheId)?.value

    if (!cacheId) {
      return ''
    }

    return `${tag}-${cacheId}`
  } catch (error) {
    return error as string
  }
}

export const getCacheOptions = async (
  tag: string,
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
): Promise<{ tags: string[] } | {}> => {
  if (typeof window !== 'undefined') {
    return {}
  }

  const cacheTag = await getCacheTag(tag)

  if (!cacheTag) {
    return {}
  }

  return { tags: [`${cacheTag}`] }
}

export const setAuthToken = async (token: string) => {
  const nextCookies = await cookies()
  nextCookies.set(keyAccessToken, token, {
    maxAge: 60 * 60 * 24 * 7,
    httpOnly: true,
    sameSite: 'strict',
    secure: process.env.NODE_ENV === 'production',
  })
}

export const setRefreshToken = async (token: string) => {
  const nextCookies = await cookies()
  nextCookies.set(keyRefreshToken, token, {
    maxAge: 60 * 60 * 24 * 7,
  })
}

export const removeAuthToken = async () => {
  const nextCookies = await cookies()
  nextCookies.set(keyAccessToken, '', {
    maxAge: -1,
  })
}

export const removeRefreshToken = async () => {
  const nextCookies = await cookies()
  nextCookies.set(keyRefreshToken, '', {
    maxAge: -1,
  })
}

export const getLocale = async (key: string) => {
  return (await cookies()).get(key)?.value
}

export const setLocale = async ({
  key,
  value,
  maxAge,
}: {
  key: string
  value: string
  maxAge?: number
}) => {
  return (await cookies()).set(key, value, {
    maxAge: maxAge || 60 * 60 * 24 * 7,
  })
}
