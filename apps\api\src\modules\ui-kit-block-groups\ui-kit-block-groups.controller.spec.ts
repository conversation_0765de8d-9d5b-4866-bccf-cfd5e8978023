import { Test, TestingModule } from '@nestjs/testing'
import { UiKitBlockGroupsController } from './ui-kit-block-groups.controller'
import { UiKitBlockGroupsService } from './ui-kit-block-groups.service'

describe('UiKitBlockGroupsController', () => {
  let controller: UiKitBlockGroupsController

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UiKitBlockGroupsController],
      providers: [UiKitBlockGroupsService],
    }).compile()

    controller = module.get<UiKitBlockGroupsController>(
      UiKitBlockGroupsController,
    )
  })

  it('should be defined', () => {
    expect(controller).toBeDefined()
  })
})
