::selection {
  background-color: #5e99ec;
  color: #fff;
}

::-moz-selection {
  background-color: #5e99ec;
  color: #fff;
}

.dark ::selection {
  background-color: #5e99ec;
  color: #fff;
}

/* Style scrollbar cho Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
  overscroll-behavior: contain;
}

/* Style scrollbar cho Webkit (Chrome, Safari) */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 9999px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background-color: #9ca3af;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Rating slider styles */
.rating-slider .slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--slider-thumb-color, #6b7280);
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.1s ease;
}

.rating-slider .slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.rating-slider .slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--slider-thumb-color, #6b7280);
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.1s ease;
}

.rating-slider .slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}
