{"collectionName": "components_dynamic_zone_related_product_categories", "info": {"displayName": "Related_Product_Categories", "icon": "bulletList"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "product_categories": {"type": "relation", "relation": "oneToMany", "target": "api::product-category.product-category"}}, "config": {}}