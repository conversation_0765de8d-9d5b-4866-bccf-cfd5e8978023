import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { paginator } from 'prisma/utils/paginator'
import { PrismaService } from 'src/modules/persistence/prisma/prisma.service'
import { CreateSiteDto } from './dto/create-site.dto'
import { UpdateSiteDto } from './dto/update-site.dto'

@Injectable()
export class SitesService {
  constructor(private readonly prisma: PrismaService) {}

  create(createSiteDto: CreateSiteDto) {
    return this.prisma.cmsSite.create({
      data: {
        name: createSiteDto.name,
        domain_id: createSiteDto.domain_id,
        user_id: createSiteDto.user_id,
        created_by: createSiteDto.created_by,
        updated_by: createSiteDto.updated_by,
        is_primary: createSiteDto.is_primary,
      },
    })
  }

  findAll(
    params: {
      skip?: number
      take?: number
      cursor?: Prisma.CmsSiteWhereUniqueInput
      where?: Prisma.CmsSiteWhereInput
      orderBy?: Prisma.CmsSiteOrderByWithRelationInput
    } = {},
  ) {
    const { skip, take = 10, cursor, where, orderBy } = params || {}

    const page = skip ? skip / take : 1

    return paginator(
      this.prisma.cmsSite,
      {
        skip,
        take,
        cursor,
        where,
        orderBy,
      },
      {
        page,
        take,
      },
    )
  }

  findOne(id: string, options?: Record<string, any>) {
    let where = { id }
    if (options) {
      where = { ...where, ...options }
    }

    return this.prisma.cmsSite.findUnique({ where })
  }

  update(
    id: string,
    updateSiteDto: UpdateSiteDto,
    options?: Record<string, any>,
  ) {
    let where = { id }
    if (options) {
      where = { ...where, ...options }
    }

    return this.prisma.cmsSite.update({
      where,
      data: {
        name: updateSiteDto.name,
        domain_id: updateSiteDto.domain_id,
        status: updateSiteDto.status,
        metadata: updateSiteDto.metadata,
        updated_by: updateSiteDto.updated_by,
      },
    })
  }

  softDelete(id: string, options?: Record<string, any>) {
    let where = { id }
    if (options) {
      where = { ...where, ...options }
    }

    return this.prisma.cmsSite.update({
      where,
      data: { deleted_at: new Date() },
    })
  }

  remove(id: string, options?: Record<string, any>) {
    let where = { id }
    if (options) {
      where = { ...where, ...options }
    }

    return this.prisma.cmsSite.delete({ where })
  }
}
