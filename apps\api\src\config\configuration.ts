const port = process.env.PORT ?? '8080'
const databaseHost = process.env.DATABASE_HOST ?? 'localhost'
const databasePort = process.env.DATABASE_PORT ?? '5432'

const jwtSecret = process.env.JWT_SECRET ?? 'secret'
const jwtExpiresIn = process.env.JWT_EXPIRES_IN ?? '1d'

const googleClientId = process.env.GOOGLE_CLIENT_ID ?? ''
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET ?? ''
const googleRedirectUri = process.env.GOOGLE_REDIRECT_URI ?? ''
const googleScope = process.env.GOOGLE_SCOPE ?? ''

export default () => ({
  port: Number.parseInt(port, 10),
  database: {
    host: databaseHost,
    port: Number.parseInt(databasePort, 10),
  },
  auth: {
    jwt: {
      secret: jwtSecret,
      expiresIn: jwtExpiresIn,
    },
  },
  google: {
    clientId: googleClientId,
    clientSecret: googleClientSecret,
    redirectUri: googleRedirectUri,
    scope: googleScope,
  },
})
