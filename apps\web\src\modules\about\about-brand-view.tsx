import {
  BHeroPage,
  BProductCategoriesByBranch,
  BTimeline,
  BVideoSlider,
} from '@ttplatform/core-page-builder/components'
import { TTimelineItem } from '@ttplatform/core-page-builder/libs'
// ---------------------------------------------------------------------------------
const _HEADING = {
  title: 'GIỚI THIỆU CAT',
  description:
    'Office ipsum you must be muted. Light lift social net support loop expectations were. Hurting underlying after cross-pollination indicators hour weeks eat can. Get web I managing need incentivization. Options point quarter then awareness pups solutionize finish helicopter. ',
  buttons: [],
  image: '/images/hero-CAT.png',
}
const _MOCK_DATA: TTimelineItem = {
  blockTitle: 'LỊCH SỬ HÌNH THÀNH',
  data: [
    {
      title: '',
      description: '',
      image: '',
    },
    {
      year: '1920s',
      title: 'The merge and a company',
      description:
        'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
      image: '/images/timeline.png',
    },
    {
      year: '1930s',
      title: 'Decade of innovation',
      description:
        'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
      image: '/images/timeline.png',
    },
    {
      year: '1940s',
      title: 'Contribution and change',
      description:
        'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
      image: '/images/timeline.png',
    },
    {
      year: '1950s',
      title: 'Becoming a multinational company',
      description:
        'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
      image: '/images/timeline.png',
    },
    {
      year: '1960s',
      title: 'Breakthroughs and great projects',
      description:
        'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
      image: '/images/timeline.png',
    },
    {
      year: '1970s',
      title: 'Firsts and Sustainability',
      description:
        'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
      image: '/images/timeline.png',
    },
    {
      year: '1980s',
      title: 'New opportunities',
      description:
        'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
      image: '/images/timeline.png',
    },
    {
      title: '',
      description: '',
    },
  ],
}
/**
 [
  {
    title: '',
    description: '',
    image: '',
  },
  {
    year: '1920s',
    title: 'The merge and a company',
    description:
      'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
    image: '/images/timeline.png',
  },
  {
    year: '1930s',
    title: 'Decade of innovation',
    description:
      'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
    image: '/images/timeline.png',
  },
  {
    year: '1940s',
    title: 'Contribution and change',
    description:
      'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
    image: '/images/timeline.png',
  },
  {
    year: '1950s',
    title: 'Becoming a multinational company',
    description:
      'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
    image: '/images/timeline.png',
  },
  {
    year: '1960s',
    title: 'Breakthroughs and great projects',
    description:
      'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
    image: '/images/timeline.png',
  },
  {
    year: '1970s',
    title: 'Firsts and Sustainability',
    description:
      'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
    image: '/images/timeline.png',
  },
  {
    year: '1980s',
    title: 'New opportunities',
    description:
      'Office ipsum you must be muted. Future-proof forward flesh price enable data deep exploratory. Just event die reach standup ',
    image: '/images/timeline.png',
  },
  {
    title: '',
    description: '',
  },
];
 */
// const _BREADCRUMB = [
//   {
//     title: 'Trang chủ',
//     href: '/',
//   },
//   {
//     title: 'Giới thiệu',
//     href: '/about',
//   },
//   {
//     title: 'CAT',
//     href: '#',
//   },
// ]
const _VIDEO = [
  {
    url: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    name: 'video 1',
    image: '/images/products/may-cong-trinh.png',
    description:
      'Office ipsum you must be muted. Kpis eco-system later. Goto big able submit driving feature most market know business. ',
  },
  {
    url: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    name: 'video 2',
    image: '/images/products/may-cong-trinh.png',
    description:
      'Office ipsum you must be muted. Kpis eco-system later. Goto big able submit driving feature most market know business. ',
  },
  {
    url: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    name: 'video 3',
    image: '/images/products/may-cong-trinh.png',
    description:
      'Office ipsum you must be muted. Kpis eco-system later. Goto big able submit driving feature most market know business. ',
  },
]
// ---------------------------------------------------------------------------------
export default function AboutBrandView() {
  return (
    <>
      <BHeroPage
        heading={_HEADING}
        styles={{}}
        image={_HEADING.image}
        video={null}
      />
      <BVideoSlider data={_VIDEO} isFullWidth />
      <BProductCategoriesByBranch />
      <BTimeline blockTitle={_MOCK_DATA.blockTitle} data={_MOCK_DATA.data} />
    </>
  )
}
