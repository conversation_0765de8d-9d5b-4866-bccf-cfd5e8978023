import { ApiProperty } from '@nestjs/swagger'
import { SiteStatus } from '@prisma/client'
import { Page } from 'src/modules/pages/entities/page.entity'

export class Site {
  @ApiProperty()
  id: string

  @ApiProperty()
  name: string

  @ApiProperty()
  domain_id: string

  @ApiProperty()
  is_primary: boolean

  @ApiProperty()
  is_hide_nav: boolean

  @ApiProperty()
  is_hide_footer: boolean

  @ApiProperty()
  is_hide_header: boolean

  @ApiProperty()
  is_hide_sidebar: boolean

  @ApiProperty({ enum: SiteStatus })
  status: SiteStatus

  @ApiProperty({ required: false })
  created_by?: string

  @ApiProperty({ required: false })
  updated_by?: string

  @ApiProperty({ required: false })
  metadata?: any

  @ApiProperty()
  created_at: Date

  @ApiProperty()
  updated_at: Date

  @ApiProperty({ required: false })
  deleted_at?: Date

  @ApiProperty({ type: () => [Page], required: false })
  pages?: Page[]
}
