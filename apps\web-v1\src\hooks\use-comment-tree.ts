import { useMemo } from 'react'

interface CommentAvatar {
  url: string
  width: number
  height: number
}

interface Comment {
  id: number
  documentId: string
  name: string
  content: string
  is_guess: boolean
  count_like: number | null
  createdAt: string
  updatedAt: string
  publishedAt: string
  avatar?: CommentAvatar
  parent: Comment | null
  children: Comment[]
}

export interface TreeComment extends Omit<Comment, 'children'> {
  children: TreeComment[]
  level: number
}

export const useCommentTree = (comments: Comment[]) => {
  return useMemo(() => {
    // Step 1: Create a map to store all comments by ID
    const commentMap = new Map<number, TreeComment>()

    // Step 2: First pass - Initialize all comments in the map
    comments.forEach((comment) => {
      commentMap.set(comment.id, {
        ...comment,
        children: [],
        level: 0,
      })
    })

    // Step 3: Find all root comments (comments without parents or with non-existent parents)
    const rootComments: TreeComment[] = []
    const processedIds = new Set<number>()

    // Helper function to process a comment and its children recursively
    const processCommentTree = (comment: Comment, level: number) => {
      const currentComment = commentMap.get(comment.id)
      if (!currentComment || processedIds.has(comment.id)) return

      currentComment.level = level
      processedIds.add(comment.id)

      // Process children from the original data
      comment.children.forEach((child) => {
        const childComment = commentMap.get(child.id)
        if (childComment) {
          childComment.level = level + 1
          currentComment.children.push(childComment)
          processedIds.add(child.id)
        }
      })

      // Look for additional children in the main comments array
      comments.forEach((potentialChild) => {
        if (
          !processedIds.has(potentialChild.id) &&
          potentialChild.parent?.id === comment.id
        ) {
          const childComment = commentMap.get(potentialChild.id)
          if (childComment) {
            childComment.level = level + 1
            currentComment.children.push(childComment)
            processCommentTree(potentialChild, level + 1)
          }
        }
      })
    }

    // Find and process root comments
    comments.forEach((comment) => {
      if (!processedIds.has(comment.id)) {
        if (!comment.parent) {
          const rootComment = commentMap.get(comment.id)
          if (rootComment) {
            rootComments.push(rootComment)
            processCommentTree(comment, 0)
          }
        }
      }
    })

    // Process any remaining comments that might be orphaned
    comments.forEach((comment) => {
      if (!processedIds.has(comment.id)) {
        const currentComment = commentMap.get(comment.id)
        if (currentComment) {
          rootComments.push(currentComment)
          processCommentTree(comment, 0)
        }
      }
    })

    // Step 4: Sort comments by creation date
    const sortByDate = (a: TreeComment, b: TreeComment) => {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    }

    const sortCommentsRecursive = (comments: TreeComment[]) => {
      comments.sort(sortByDate)
      comments.forEach((comment) => {
        if (comment.children.length > 0) {
          sortCommentsRecursive(comment.children)
        }
      })
    }

    sortCommentsRecursive(rootComments)

    // Debug logging
    console.log('Tree structure:', {
      rootComments: rootComments.map((comment) => ({
        id: comment.id,
        name: comment.name,
        level: comment.level,
        parentId: comment.parent?.id,
        children: comment.children.map((child) => ({
          id: child.id,
          name: child.name,
          level: child.level,
          parentId: child.parent?.id,
        })),
      })),
    })

    return {
      comments: rootComments,
      getCommentLevel: (level: number): string => {
        switch (level) {
          case 0:
            return 'Ông'
          case 1:
            return 'Cha'
          case 2:
            return 'Cháu'
          default:
            return `Cháu ${level}`
        }
      },
    }
  }, [comments])
}
