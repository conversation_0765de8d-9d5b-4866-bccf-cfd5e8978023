import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { T_UserReturn } from '../types'
import devtoolConfig from './devtool'

import { getAccount, login, logout, register } from '../data/auth'

type AuthUserType = T_UserReturn

type T_AuthState = {
  user: AuthUserType | null
  initialized: boolean
  authenticated: boolean
  fetched: boolean
  loading: boolean
  error: string | null
  message: string | null
}

type T_AuthActions = {
  getSession: () => Promise<void>
  onLogin: (payload: {
    email: string
    password: string
  }) => Promise<AuthUserType>
  onSignUp: (payload: {
    name: string
    email: string
    password: string
  }) => Promise<AuthUserType>
  onUpdateUser: (user: AuthUserType) => void
  onLogout: () => Promise<void>
}

const initialState: T_AuthState = {
  user: null,
  authenticated: false,
  initialized: false,
  fetched: false,
  loading: false,
  error: null,
  message: null,
}

const useAuth = create<T_AuthState & T_AuthActions>()(
  devtools(
    (set) => ({
      ...initialState,
      getSession: async () => {
        set({ loading: true })
        try {
          const user = await getAccount()
          if (!user) return

          set({
            user: user as T_UserReturn,
            fetched: true,
            authenticated: true,
            initialized: true,
            loading: false,
          })
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error'
          set({
            fetched: true,
            error: errorMessage,
            loading: false,
            authenticated: false,
          })

          throw new Error(errorMessage)
        } finally {
          set({ loading: false })
        }
      },
      onSignIn: async (payload: { email: string; password: string }) => {
        set({ loading: true })
        try {
          const user = await login(payload)
          set({
            user: user as T_UserReturn,
            authenticated: true,
            initialized: true,
            loading: false,
            error: null,
          })

          return user
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, authenticated: false })

          throw new Error(errorMessage)
        } finally {
          set({ loading: false })
        }
      },
      onSignUp: async (payload: {
        name: string
        email: string
        password: string
      }) => {
        set({ loading: true })
        try {
          const user = await register(payload)
          set({
            user: user as T_UserReturn,
            authenticated: true,
            initialized: true,
            loading: false,
          })

          return user
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error'
          set({ loading: false, error: errorMessage, authenticated: false })

          throw new Error(errorMessage)
        } finally {
          set({ loading: false })
        }
      },
      onUpdateUser: (user: AuthUserType) => {
        set((state) => ({
          ...state,
          user: {
            ...state.user, // Keep current user state
            ...user, // Update with new user data
          },
        }))
      },
      onLogout: async () => {
        try {
          set({ loading: true })
          await logout()
          set((state) => ({
            ...state,
            user: null,
            fetched: true,
            loading: false,
            authenticated: false,
          }))
          window.location.href = '/'
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error'
          set((state) => ({
            ...state,
            loading: false,
            error: errorMessage,
            authenticated: false,
            fetched: true,
          }))

          throw new Error(errorMessage)
        } finally {
          set({ loading: false })
        }
      },
    }),
    devtoolConfig('auth', 'ttplatform'),
  ),
)
export default useAuth
