import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiHeaders, ApiQuery } from '@nestjs/swagger'
import { fResponse } from 'prisma/utils/format-response'
import { JwtAuthGuard } from '../auth/guards/jwt-auth'
import { UserService } from '../user/user.service'
import { DomainsService } from './domains.service'
import { CreateDomainDto } from './dto/create-domain.dto'
import { UpdateDomainDto } from './dto/update-domain.dto'

@Controller('domains')
export class DomainsController {
  constructor(
    private readonly domainsService: DomainsService,
    private readonly userService: UserService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        domain: { type: 'string' },
        is_active: { type: 'boolean' },
        metadata: { type: 'object' },
      },
    },
  })
  async create(@Body() createDomainDto: CreateDomainDto, @Request() req) {
    try {
      // get current user
      const user_id = req.user.user_id

      const payload = {
        ...createDomainDto,
        user_id,
        created_by: user_id,
        updated_by: user_id,
      }

      const data = await this.domainsService.create(payload)
      return fResponse({ data, message: 'Domain was created!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  @ApiQuery({ name: 'skip', type: Number, required: false })
  @ApiQuery({ name: 'take', type: Number, required: false })
  @ApiQuery({ name: 'cursor', type: String, required: false })
  @ApiQuery({ name: 'orderBy', type: String, required: false })
  async findAll(
    @Request() req,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('cursor') cursor = undefined,
    @Query('orderBy') orderBy = undefined,
  ) {
    try {
      const user_id = req.user.user_id

      const { data, metadata } = await this.domainsService.findAll({
        skip,
        take,
        cursor,
        where: {
          user_id: user_id,
        },
        orderBy,
      })
      return fResponse({ data, metadata })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  async findOne(@Param('id') id: string, @Request() req) {
    try {
      const user_id = req.user.user_id

      const data = await this.domainsService.findOne(id, { user_id })
      return fResponse({ data, message: 'Domain is found!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':id')
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  async update(
    @Param('id') id: string,
    @Body() updateDomainDto: UpdateDomainDto,
    @Request() req,
  ) {
    try {
      const user_id = req.user.user_id

      const data = await this.domainsService.update(id, updateDomainDto, {
        user_id,
      })
      return fResponse({ data, message: 'Domain was updated!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiQuery({ name: 'isSoft', type: Boolean, required: false })
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  async remove(
    @Param('id') id: string,
    @Request() req,
    @Query('isSoft') isSoft = false,
  ) {
    try {
      const user_id = req.user.user_id

      if (!isSoft) {
        await this.domainsService.softDelete(id, { user_id })
      } else {
        await this.domainsService.remove(id, { user_id })
      }

      return fResponse({ data: null, message: 'Domain was deleted!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }
}
