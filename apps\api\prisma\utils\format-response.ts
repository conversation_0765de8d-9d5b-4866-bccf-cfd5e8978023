/////////////////////////////////////////////
// RESPONSE
/////////////////////////////////////////////

type TResponseProps<T> = {
  data: T
  metadata?: any
  message?: string
  code?: number
  error?: boolean
  success?: boolean
}

export const fResponse = <T>({
  data,
  metadata = null,
  message = 'Success',
  code = 200,
  error = false,
  success = true,
}: TResponseProps<T>) => {
  return {
    data,
    ...(metadata && { metadata }),
    message,
    code,
    error,
    success,
  }
}
