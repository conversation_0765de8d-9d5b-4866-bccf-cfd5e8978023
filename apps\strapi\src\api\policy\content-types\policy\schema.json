{"kind": "collectionType", "collectionName": "policies", "info": {"singularName": "policy", "pluralName": "policies", "displayName": "Policy"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "content": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "excerpt": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}}}