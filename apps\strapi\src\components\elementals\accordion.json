{"collectionName": "components_elementals_accordions", "info": {"displayName": "Accordion"}, "options": {}, "attributes": {"background_color": {"type": "customField", "customField": "plugin::color-picker.color", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"}, "text_color": {"type": "customField", "customField": "plugin::color-picker.color", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"}, "item_border_radius": {"type": "integer"}, "items": {"type": "component", "component": "items.accordion-item", "repeatable": true}, "image": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "show_order": {"type": "boolean", "default": false}}, "config": {}}