import { SectionTitle } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { ArrowRight } from 'lucide-react'
import { HexagonIconButton } from '../../../../../../packages/core/page-builder/src/components/ui/custom-buttons/hexagon-button'

//---------------------------------------------------------------------------------
const __ITEMS = [
  {
    image: '/images/products/related-1.png',
    title: 'CONSTRUCTION',
    excerpt:
      'A growing construction industry needs a strong partner. As a leader in the industry, we want to help grow your business with affordable equipment that positively impacts your bottom line through fuel savings and increased productivity. A full line of Cat®️ machine is ready for any job with the durability and performance you need.',
  },
  {
    image: '/images/products/related-2.png',
    title: 'ELECTRIC POWER',
    excerpt:
      'Cat Electric Power has decades of experience and unmatched expertise meeting various commercial & industrial electric power needs. Our experts are ready to help you design a power generator system to your specifications and stand by your side for the life of your equipment with the support of our global Cat® dealer network for products',
  },
  {
    image: '/images/products/related-3.png',
    title: 'INDUSTRIAL POWER',
    excerpt:
      'Cat® industrial engines offer a diverse product range from 0.5L to 32L to meet the needs of equipment manufacturers across the world. Cat engines power thousands of unique applications for many of the leading OEMs whose equipment perfectly complements customers',
  },
]
//---------------------------------------------------------------------------------
export default function DetailsRelatedTechnologyAndServices() {
  return (
    <section id="CONG_NGHE" className="bg-gray-50">
      <div className="w-full container max-w-screen-xl mx-auto px-4">
        <div className="flex flex-col gap-10 py-10 lg:py-20">
          <SectionTitle text="công nghệ và dịch vụ liên quan" align="left" />
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6">
            {__ITEMS.map((item, idx) => (
              <ItemRelated key={idx} item={item} />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
//---------------------------------------------------------------------------------
type IProps = {
  item: any
}
function ItemRelated({ item }: IProps) {
  return (
    <div
      className="w-full h-auto flex flex-col bg-white rounded-lg overflow-hidden "
      style={{
        boxShadow:
          '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
      }}
    >
      <div
        className="w-full h-auto aspect-[3/2]"
        style={{
          backgroundImage: `url(${item.image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />
      <div
        className="w-full h-auto py-2 px-6 flex items-center justify-between gap-4"
        style={{
          background:
            'linear-gradient(90deg, #FC0 73.08%, #FFD326 87.02%, #FFDE59 100%)',
        }}
      >
        <Typography variant="h4" className="text-gray-900 font-bold">
          {item.title}
        </Typography>
        <HexagonIconButton icon={<ArrowRight />} />
      </div>
      <div className="w-full h-auto p-6 ">
        <Typography
          variant="body2"
          className="text-gray-700 font-bold line-clamp-3"
        >
          {item.excerpt}
        </Typography>
      </div>
    </div>
  )
}
