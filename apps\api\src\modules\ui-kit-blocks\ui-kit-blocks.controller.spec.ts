import { Test, TestingModule } from '@nestjs/testing'
import { UiKitBlocksController } from './ui-kit-blocks.controller'
import { UiKitBlocksService } from './ui-kit-blocks.service'

describe('UiKitBlocksController', () => {
  let controller: UiKitBlocksController

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UiKitBlocksController],
      providers: [UiKitBlocksService],
    }).compile()

    controller = module.get<UiKitBlocksController>(UiKitBlocksController)
  })

  it('should be defined', () => {
    expect(controller).toBeDefined()
  })
})
