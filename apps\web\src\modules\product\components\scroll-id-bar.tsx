'use client'

import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import Image from 'next/image'
import { useState } from 'react'
//---------------------------------------------------------------------------------
type ScrollIdBarProps = {
  listLink: any
}
//---------------------------------------------------------------------------------
export function ScrollIdBar({ listLink }: ScrollIdBarProps) {
  const [isExpand, setIsExpand] = useState(true)
  return (
    <div
      className={cn(
        'flex flex-col fixed z-50 left-0 bottom-0 pt-4 px-4 bg-gray-50 rounded-tr-sm rounded-br-sm transition-all ease-in-out duration-300',
        isExpand ? 'w-[125px]' : 'w-auto',
      )}
      style={{
        boxShadow:
          '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
      }}
    >
      <div
        onClick={() => setIsExpand(!isExpand)}
        className="max-w-max cursor-pointer"
      >
        {isExpand ? (
          <ChevronLeft width={20} height={20} />
        ) : (
          <ChevronRight width={20} height={20} />
        )}
      </div>

      <div className="flex flex-col items-center">
        {listLink.map((item: any, idx: number) => (
          <a
            key={idx}
            href={item.url || '#'}
            className=" w-full not-last:border-b border-gray-200"
          >
            <div className="w-full flex flex-col py-4 gap-2 items-center justify-center group">
              <Image
                src={item.icon}
                alt="quick link icon"
                width={20}
                height={20}
                className="text-red-400 hover:text-primary transition-colors"
              />
              {isExpand ? (
                <Typography
                  variant={'body2'}
                  className="text-gray-400 text-center font-medium"
                >
                  {item.title}
                </Typography>
              ) : null}
            </div>
          </a>
        ))}
      </div>
    </div>
  )
}
