import { SectionTitle } from '@ttplatform/core-page-builder/components'
import {
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  ToggleGroup,
  ToggleGroupItem,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { Columns2, LayoutGrid } from 'lucide-react'
import { useCallback } from 'react'
import { CardProduct } from '../../../../../../packages/core/page-builder/src/components/ui/cards/card-product'
import { HexagonButton } from '../../../../../../packages/core/page-builder/src/components/ui/custom-buttons/hexagon-button'
//---------------------------------------------------------------------------------
const __LIST_PRODUCT = [
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
]

const _DISPLAY_MODE_LIST = [
  {
    name: 'Grid',
    slug: 'grid',
  },
  {
    name: 'Table',
    slug: 'table',
  },
]
const _TABLE_HEAD = [
  {
    name: 'So sánh',
    className: 'max-w-max p-0',
  },
  {
    name: 'Model',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Công suất hiệu dụng',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Dung tích gầu',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Khối lượng vận hành',
    className: 'w-[40%] md:w-[50%] p-0',
  },
]
//---------------------------------------------------------------------------------
type IProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}
export default function FilterResults({ filters, onFilters }: IProps) {
  const handleChangeDisplayMode = useCallback(
    (slug: string) => {
      onFilters('displayMode', slug)
    },
    [onFilters],
  )

  const renderDisplayMode = (
    <div className="flex gap-4 items-center">
      <Typography variant="body2" className="text-gray-700 font-medium">
        Chế độ hiển thị:
      </Typography>
      <ToggleGroup
        type="single"
        value={filters?.displayMode}
        onValueChange={handleChangeDisplayMode}
      >
        {_DISPLAY_MODE_LIST.map((item: any, idx: number) => (
          <ToggleGroupItem
            key={idx}
            value={item.slug}
            aria-label={item.name}
            className="[&_svg]:text-gray-500 data-[state=on]:[&_svg]:text-gray-800"
          >
            {item.name === 'Grid' ? (
              <LayoutGrid className="h-4 w-4" />
            ) : (
              <Columns2 className="h-4 w-4" />
            )}
          </ToggleGroupItem>
        ))}
      </ToggleGroup>
    </div>
  )
  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-4 items-center justify-between">
        <SectionTitle text="MODEL SẢN PHẨM" align="left" />
        {renderDisplayMode}
      </div>
      {/* Grid */}
      {filters?.displayMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-10">
          {__LIST_PRODUCT.map((item: any, idx: number) => (
            <CardProduct key={idx} product={item} />
          ))}
        </div>
      ) : null}
      {/* Table */}
      {filters?.displayMode === 'table' ? (
        <Table className="border rounded-2xl">
          <TableHeader className="bg-[#FFCC00]">
            <TableRow>
              {_TABLE_HEAD.map((item: any, idx: number) => (
                <TableHead key={idx} className={cn('p-0', item?.className)}>
                  <Typography
                    variant={'body2'}
                    className="text-[#182230] font-medium px-4 md:px-6 py-3"
                  >
                    {item?.name}
                  </Typography>
                </TableHead>
              ))}
              <TableHead className="w-[40%] md:w-[50%] p-0"></TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {__LIST_PRODUCT.map((item: any, idx: number) => (
              <TableRow key={idx}>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Checkbox className="data-[state=checked]:text-black" />
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Typography variant={'body2'} className="text-gray-800">
                    {item?.name}
                  </Typography>
                </TableCell>

                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Typography variant={'body2'} className="text-gray-800">
                    {item?.cong_suat}
                  </Typography>
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Typography variant={'body2'} className="text-gray-800">
                    {item?.dung_tich_gau}
                  </Typography>
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Typography variant={'body2'} className="text-gray-800">
                    {item?.khoi_luong_van_hanh}
                  </Typography>
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <HexagonButton />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : null}
    </div>
  )
}
