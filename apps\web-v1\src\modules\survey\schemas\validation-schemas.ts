import { z } from 'zod'

// Base validation schemas
export const baseSchemas = {
  email: z.string().email('<PERSON><PERSON> không hợp lệ'),
  phone: z.string().regex(/^[0-9+\-\s()]+$/, '<PERSON><PERSON> điện thoại không hợp lệ'),
  required: z.string().min(1, 'Trường này là bắt buộc'),
  optional: z.string().optional(),
} as const

// Survey-specific schemas
export const surveySchemas = {
  // Confirmation step schema
  confirmation: z.object({
    contact_name: baseSchemas.required.refine(
      (val) => val.trim().length >= 2,
      'Họ tên phải có ít nhất 2 ký tự',
    ),
    contact_phone: baseSchemas.phone,
    contact_email: baseSchemas.email,
    company_name: baseSchemas.required,
    company_address: baseSchemas.required,
    company_tax_code: baseSchemas.optional,
  }),

  // Rating validation
  rating: z.number().min(1).max(10),

  // Open-ended validation with dynamic constraints
  openEnded: (minLength?: number, maxLength?: number) => {
    let schema: z.ZodType<string> = baseSchemas.required

    if (minLength) {
      schema = schema.refine(
        (val) => val.length >= minLength,
        `Vui lòng nhập ít nhất ${minLength} ký tự`,
      )
    }

    if (maxLength) {
      schema = schema.refine(
        (val) => val.length <= maxLength,
        `Vui lòng nhập không quá ${maxLength} ký tự`,
      )
    }

    return schema
  },
} as const

// Type exports
export type ConfirmationFormData = z.infer<typeof surveySchemas.confirmation>

// Field type detection utilities
export const FIELD_TYPES = {
  EMAIL: ['email', 'mail'],
  PHONE: ['phone', 'điện_thoại', 'sdt', 'tel'],
} as const

export const getFieldType = (
  fieldId: string,
): keyof typeof FIELD_TYPES | null => {
  const normalizedId = fieldId.toLowerCase().replace(/[_\s]/g, '')

  for (const [type, patterns] of Object.entries(FIELD_TYPES)) {
    if (
      patterns.some((pattern) =>
        normalizedId.includes(pattern.replace(/[_\s]/g, '')),
      )
    ) {
      return type as keyof typeof FIELD_TYPES
    }
  }
  return null
}

// Validation helper
export const validateFieldByType = (value: any, fieldId: string) => {
  const fieldType = getFieldType(fieldId)

  if (!fieldType || !value) {
    return { isValid: true, errors: [] }
  }

  try {
    switch (fieldType) {
      case 'EMAIL':
        baseSchemas.email.parse(value)
        break
      case 'PHONE':
        baseSchemas.phone.parse(value)
        break
      default:
        return { isValid: true, errors: [] }
    }

    return { isValid: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map((e) => e.message),
      }
    }
    return { isValid: false, errors: ['Validation error'] }
  }
}
