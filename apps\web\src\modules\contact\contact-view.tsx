import {
  BContactBranches,
  BHeroPage,
} from '@ttplatform/core-page-builder/components'
import ContactDetails from './components/contact-details'
import ContactForm from './components/contact-form'
//----------------------------------------------------------------------------
const _HEADING = {
  title: 'LIÊN HỆ',
  description:
    'Office ipsum you must be muted. Light lift social net support loop expectations were. Hurting underlying after cross-pollination indicators hour weeks eat can. Get web I managing need incentivization. Options point quarter then awareness pups solutionize finish helicopter. ',
  buttons: [],
  image: '/images/contact/contact-hero.png',
}
// const _BREADCRUMB = [
//   {
//     title: 'Trang chủ',
//     href: '/',
//   },
//   {
//     title: '<PERSON>ên hệ',
//     href: '/contact',
//   },
// ]
//----------------------------------------------------------------------------
export default function ContactView() {
  return (
    <>
      <BHeroPage
        heading={_HEADING}
        styles={{}}
        image={_HEADING.image}
        video={null}
      />
      <section>
        <div className="w-full container max-w-screen-xl mx-auto px-4 py-10">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            <ContactDetails />
            <ContactForm />
          </div>
        </div>
      </section>
      <BContactBranches />
    </>
  )
}
