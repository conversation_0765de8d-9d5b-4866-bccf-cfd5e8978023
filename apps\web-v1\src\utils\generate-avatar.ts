interface AvatarOptions {
  size?: number
  background?: string
  rounded?: boolean
}

/**
 * Generate avatar from initials
 */
export const generateAvatarFromInitials = (
  name: string,
  size: number = 40,
  options: AvatarOptions = {},
): string => {
  if (!name?.trim()) {
    return generateDefaultAvatar(size)
  }

  const { background, rounded = true } = options

  // Extract initials (supports Unicode)
  const initials = name
    .trim()
    .split(/\s+/)
    .map((word) => Array.from(word)[0]?.toUpperCase())
    .filter(Boolean)
    .slice(0, 2)
    .join('')

  const bgColor = background || generateColorFromName(name)

  return createSVGAvatar(initials, size, bgColor, rounded)
}

/**
 * Generate consistent color from name
 */
function generateColorFromName(name: string): string {
  const colors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FECA57',
    '#FF9FF3',
    '#54A0FF',
    '#5F27CD',
    '#00D2D3',
    '#FF9F43',
    '#10AC84',
    '#EE5A24',
    '#0984E3',
    '#A29BFE',
    '#FD79A8',
  ]

  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = (hash << 5) - hash + name.charCodeAt(i)
    hash = hash & hash
  }

  return colors[Math.abs(hash) % colors.length]
}

/**
 * Create SVG avatar
 */
function createSVGAvatar(
  initials: string,
  size: number,
  background: string,
  rounded: boolean,
): string {
  const fontSize = Math.floor(size * 0.4)
  const shape = rounded
    ? `<circle cx="${size / 2}" cy="${size / 2}" r="${size / 2}" fill="${background}"/>`
    : `<rect width="${size}" height="${size}" fill="${background}"/>`

  const svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
    ${shape}
    <text x="${size / 2}" y="${size / 2}" text-anchor="middle" dy="0.35em" 
          font-family="Arial,sans-serif" font-size="${fontSize}" font-weight="bold" fill="white">
      ${initials}
    </text>
  </svg>`

  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`
}

/**
 * Default avatar for empty names
 */
function generateDefaultAvatar(size: number): string {
  return createSVGAvatar('?', size, '#E0E0E0', true)
}

/**
 * Check if URL is valid avatar
 */
export const isValidAvatarUrl = (url: string): boolean => {
  return Boolean(
    url && (url.startsWith('data:image/') || url.startsWith('http')),
  )
}
