'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { Input, Label, Typography } from '@ttplatform/ui/components'

export default function RecruitmentForm() {
  return (
    <div
      className="w-full flex flex-col gap-y-6 p-6 bg-gray-50 rounded-lg"
      style={{
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Typography variant={'h6'} className="uppercase text-gray-800">
        ỨNG TUYỂN CÔNG VIỆC
      </Typography>
      <div className="flex flex-col gap-y-4">
        <div className="flex flex-col w-full gap-2">
          <Label htmlFor="name" className="font-medium text-gray-600">
            Họ và tên
          </Label>
          <Input type="text" id="name" className="bg-white" />
        </div>
        <div className="flex flex-col w-full gap-2">
          <Label htmlFor="email" className="font-medium text-gray-600">
            Email
          </Label>
          <Input type="email" id="email" className="bg-white" />
        </div>
        <div className="flex flex-col w-full gap-2">
          <Label htmlFor="phone" className="font-medium text-gray-600">
            Số điện thoại
          </Label>
          <Input type="text" id="phone" className="bg-white" />
        </div>
        <div className="flex flex-col w-full gap-2">
          <Label htmlFor="cv" className="font-medium text-gray-600">
            Resume/CV
          </Label>
          <Input type="text" id="phone" className="bg-white" />
        </div>
        <div className="flex flex-col w-full gap-2">
          <Label htmlFor="cover_letter" className="font-medium text-gray-600">
            Cover Letter
          </Label>
          <Input type="text" id="phone" className="bg-white" />
        </div>
      </div>
      <MainButton label="Ứng tuyển " />
    </div>
  )
}
