import {
  Checkbox,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Typography,
} from '@ttplatform/ui/components'
import { useCallback } from 'react'
//---------------------------------------------------------------------------------
const _CATEGORY_LIST = [
  {
    id: 1,
    slug: 'bo-phan-ky-thuat-e-t',
    name: 'bộ phận kỹ thuật E&T',
  },
  {
    id: 2,
    slug: 'phong-kinh-doanh-may-phat-dien',
    name: '<PERSON>òng kinh doanh máy phát điện',
  },
  {
    id: 3,
    slug: 'phong-kinh-doanh-may-cong-trinh',
    name: 'Phòng kinh doanh máy công trình',
  },
  {
    id: 4,
    slug: 'phong-kinh-doanh-may-thuy',
    name: '<PERSON>òng kinh doanh máy thuỷ',
  },
]

const _WORKING_FORM_LIST = [
  {
    id: 1,
    slug: 'full-time',
    name: 'Full-time',
  },
  {
    id: 2,
    slug: 'part-time',
    name: 'Part time',
  },
]
const _LOCATION_LIST = [
  {
    id: 1,
    slug: 'ha-noi',
    name: 'Hà Nội',
  },
  {
    id: 2,
    slug: 'ho-chi-minh',
    name: 'Hồ Chí Minh',
  },
  {
    id: 3,
    slug: 'da-nang',
    name: 'Đà Nẵng',
  },
  {
    id: 4,
    slug: 'can-tho',
    name: 'Cần Thơ',
  },
  {
    id: 5,
    slug: 'hue',
    name: 'Huế',
  },
]
//---------------------------------------------------------------------------------
type FilterBarProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}
//---------------------------------------------------------------------------------
export default function FilterBar({ filters, onFilters }: FilterBarProps) {
  const handleFilterCategory = useCallback(
    (slug: string) => {
      onFilters('category', slug)
    },
    [onFilters],
  )

  const handleFilterWorkingForm = useCallback(
    (slug: string) => {
      onFilters('workingForm', slug)
    },
    [onFilters],
  )
  const handleFilterLocation = useCallback(
    (slug: string) => {
      onFilters('location', slug)
    },
    [onFilters],
  )
  const handleFilterShow = useCallback(
    (value: string) => {
      onFilters('show_only_valid_job_postings', value)
    },
    [onFilters],
  )

  const renderFilterCategory = (
    <div className="flex flex-col gap-4">
      <Typography
        variant="body1"
        className="text-gray-800 uppercase font-semibold"
      >
        Phòng ban/bộ phận
      </Typography>
      <RadioGroup
        value={filters?.category}
        className="gap-4"
        onValueChange={handleFilterCategory}
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value={'all'} id={'all'} />
          <Typography variant={'body2'} className="capitalize">
            Tất cả
          </Typography>
        </div>
        {_CATEGORY_LIST.map((category: any, idx: number) => (
          <div key={idx} className="flex items-center space-x-2">
            <RadioGroupItem value={category?.slug} id={category?.slug} />
            <Typography variant={'body2'} className="capitalize">
              {category?.name}
            </Typography>
          </div>
        ))}
      </RadioGroup>
    </div>
  )

  const renderFilterWorkingForm = (
    <div className="flex flex-col gap-4">
      <Typography
        variant="body1"
        className="text-gray-800 uppercase font-semibold"
      >
        Hình thức
      </Typography>
      <RadioGroup
        value={filters?.workingForm}
        className="gap-4"
        onValueChange={handleFilterWorkingForm}
      >
        {_WORKING_FORM_LIST.map((item: any, idx: number) => (
          <div key={idx} className="flex items-center space-x-2">
            <RadioGroupItem value={item?.slug} id={item?.slug} />
            <Typography variant={'body2'} className="capitalize">
              {item?.name}
            </Typography>
          </div>
        ))}
      </RadioGroup>
    </div>
  )
  const renderFilterLocation = (
    <div className="flex flex-col gap-4">
      <Typography
        variant="body1"
        className="text-gray-800 uppercase font-semibold"
      >
        Vị trí tuyển dụng
      </Typography>
      <Select onValueChange={handleFilterLocation}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Chọn tỉnh/thành phố" />
        </SelectTrigger>
        <SelectContent>
          {_LOCATION_LIST.map((item: any, idx: number) => (
            <SelectItem key={idx} value={item?.slug}>
              {item?.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
  const renderFilterShow = (
    <div className="flex items-center space-x-2">
      <Checkbox
        id="show"
        onCheckedChange={(value: any) => handleFilterShow(value)}
        defaultChecked={filters?.show_only_valid_job_postings}
      />
      <Typography variant={'body2'} className="text-gray-700 font-medium">
        Chỉ hiển thị tin tuyển dụng còn hiệu lực
      </Typography>
    </div>
  )
  return (
    <div
      className="max-h-dvh sticky top-0 overflow-y-auto flex flex-col gap-8 border border-gray-200 rounded-xl p-6 bg-white"
      style={{
        boxShadow:
          '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Typography variant="h5" className="text-gray-800 uppercase font-bold">
        LỌC VỊ TRÍ TUYỂN DỤNG
      </Typography>
      <div className="flex flex-col gap-10">
        {renderFilterCategory}
        {renderFilterWorkingForm}
        {renderFilterLocation}
        {renderFilterShow}
      </div>
    </div>
  )
}
