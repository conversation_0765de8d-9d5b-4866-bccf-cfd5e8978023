import { Body, Controller, Get, Post } from '@nestjs/common'

import type { CreateUserDTO } from './dto'
import { UserService } from './user.service'

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  async createUser(@Body() createUserDto: CreateUserDTO) {
    return this.userService.create(createUserDto)
  }

  @Get('/email')
  async getUsers() {
    return this.userService.findByEmail('<EMAIL>')
  }
}
