import React from 'react'

interface HexagonShadowIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
  withBoxShadow?: boolean
}

export const HexagonShadowIcon: React.FC<HexagonShadowIconProps> = ({
  size = 40,
  color = '#FFCC00',
  className = '',
}) => {
  const computedHeight = (size * 52) / 57
  return (
    <svg
      width={size}
      height={computedHeight}
      viewBox="0 0 57 52"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_dd_18686_13099)">
        <path
          d="M50.0243 20.4571C50.376 21.0715 50.5518 21.3788 50.6207 21.7041C50.6818 21.9921 50.6818 22.2891 50.6207 22.5771C50.5518 22.9025 50.376 23.2097 50.0243 23.8242L40.8477 39.8575C40.4763 40.5065 40.2906 40.831 40.0264 41.0673C39.7927 41.2763 39.5158 41.4345 39.214 41.5312C38.873 41.6406 38.4906 41.6406 37.7257 41.6406H19.6073C18.8424 41.6406 18.46 41.6406 18.119 41.5312C17.8172 41.4345 17.5403 41.2763 17.3066 41.0673C17.0424 40.831 16.8567 40.5065 16.4853 39.8575L7.30874 23.8242C6.95705 23.2097 6.78121 22.9025 6.71227 22.5771C6.65125 22.2891 6.65125 21.9921 6.71227 21.7041C6.78121 21.3788 6.95705 21.0715 7.30874 20.4571L16.4853 4.42373C16.8567 3.77476 17.0424 3.45028 17.3066 3.214C17.5403 3.00497 17.8172 2.84678 18.119 2.75001C18.46 2.64062 18.8424 2.64062 19.6073 2.64063L37.7257 2.64063C38.4906 2.64063 38.873 2.64063 39.214 2.75001C39.5158 2.84678 39.7927 3.00497 40.0264 3.214C40.2906 3.45028 40.4763 3.77476 40.8477 4.42373L50.0243 20.4571Z"
          fill={color}
        />
      </g>
      <defs>
        <filter
          id="filter0_dd_18686_13099"
          x="0.666504"
          y="0.640625"
          width="56"
          height="51"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_18686_13099"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.12 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_18686_13099"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_18686_13099"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="4" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.18 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_18686_13099"
            result="effect2_dropShadow_18686_13099"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_18686_13099"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
