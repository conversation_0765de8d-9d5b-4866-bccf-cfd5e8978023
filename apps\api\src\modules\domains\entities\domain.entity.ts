import { ApiProperty } from '@nestjs/swagger'
import { Site } from 'src/modules/sites/entities/site.entity'

export class Domain {
  @ApiProperty()
  id: string

  @ApiProperty()
  name: string

  @ApiProperty()
  domain: string

  @ApiProperty()
  is_active: boolean

  @ApiProperty({ required: false })
  metadata?: any

  @ApiProperty()
  created_at: Date

  @ApiProperty()
  updated_at: Date

  @ApiProperty({ required: false })
  deleted_at?: Date

  @ApiProperty({ type: () => [Site], required: false })
  sites?: Site[]
}
