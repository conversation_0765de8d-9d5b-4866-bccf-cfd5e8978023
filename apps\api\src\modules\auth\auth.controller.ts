import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common'

import { ApiBody, ApiHeaders } from '@nestjs/swagger'
import { CreateUserDTO } from '../user/dto'
import { UserService } from '../user/user.service'
import { AuthService } from './auth.service'
import { Public } from './decorators/public.decorators'
import { UserLoginDTO } from './dto'
import { JwtAuthGuard, LocalAuthGuard, RefreshAuthGuard } from './guards'
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userService: UserService,
  ) {}

  // 📝 Sign up (register)
  @Public()
  @Post('signup')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        email: { type: 'string' },
        password: { type: 'string' },
        image: { type: 'string' },
      },
    },
  })
  async signup(@Body() createUserDto: CreateUserDTO) {
    try {
      return this.authService.signup(createUserDto)
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  // 🔐 Sign in (login)
  @Public()
  @UseGuards(LocalAuthGuard)
  @Post('signin')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string' },
        password: { type: 'string' },
      },
    },
  })
  async signin(@Body() userLoginDto: UserLoginDTO) {
    try {
      return this.authService.login(userLoginDto) // LocalAuthGuard already validates
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  // ♻️ Refresh token
  @UseGuards(RefreshAuthGuard)
  @Post('refresh')
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Refresh token',
      required: true,
    },
  ])
  async refresh(@Request() req) {
    try {
      return this.authService.refreshToken(req.user.user_id, req.user.name)
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  // 👤 Get current logged-in user (requires JWT guard)
  @UseGuards(JwtAuthGuard)
  @Get('me')
  async me(@Request() req) {
    try {
      const item = await this.userService.findById(req.user.user_id)
      if (!item) throw new NotFoundException('User not found')

      return {
        data: {
          id: item.id,
          name: item.name,
          email: item.email,
          image: item.image,
          email_verified: item.email_verified,
        },
      }
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  // 🚪 Logout (invalidate refresh token)
  @UseGuards(JwtAuthGuard)
  @Delete('signout')
  async signout(@Request() req) {
    try {
      await this.authService.signout(req.user.user_id)
      return {
        message: 'Logged out successfully',
        statusCode: 200,
      }
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }
}
