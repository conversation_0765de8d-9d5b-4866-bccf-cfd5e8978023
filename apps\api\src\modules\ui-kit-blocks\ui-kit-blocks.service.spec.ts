import { Test, TestingModule } from '@nestjs/testing'
import { UiKitBlocksService } from './ui-kit-blocks.service'

describe('UiKitBlocksService', () => {
  let service: UiKitBlocksService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UiKitBlocksService],
    }).compile()

    service = module.get<UiKitBlocksService>(UiKitBlocksService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })
})
