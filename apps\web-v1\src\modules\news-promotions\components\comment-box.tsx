'use client'
import { formatDate } from '@ttplatform/core-page-builder/libs'
import { cn } from '@ttplatform/ui/lib'
import { MessageSquare } from 'lucide-react'
import Image from 'next/image'
import { useState } from 'react'
import { RenderImageUrlStrapi } from '~/src/components/render-imageUrl-strapi'
import { generateAvatarFromInitials } from '~/src/utils/generate-avatar'
import CommentInput from './comment-input'
import LikeButton from './like-button'

export interface CommentData {
  id: string
  author: {
    name: string
    avatarSrc?: string
  }
  content: string
  date: string // ISO string
  likes: number
  replies?: CommentData[]
  parentId?: string
}

interface CommentProps {
  comment: any
  level?: number
  onAddReply: (parentId: string, text: string, name: string) => void
  isSubmitting?: boolean
}

export const CommentBox = ({
  comment,
  level = 0,
  onAddReply,
  isSubmitting = false,
}: CommentProps) => {
  const [showReplyInput, setShowReplyInput] = useState(false)
  const maxLevel = 2
  const currentLevel = level > maxLevel ? maxLevel : level

  const handleAddReply = async (text: string, name: string) => {
    if (isSubmitting) return
    await onAddReply(comment?.documentId || '', text, name)
    setShowReplyInput(false)
  }

  const getMarginClass = () => {
    switch (currentLevel) {
      case 1:
        return 'ml-12'
      case 2:
        return 'ml-24'
      default:
        return ''
    }
  }

  // Generate avatar URL based on user's name if no avatar is provided
  const getAvatarUrl = () => {
    if (comment.avatar?.url) {
      console.log('🚀 ~ getAvatarUrl ~ comment.avatar:', comment.avatar)
      return RenderImageUrlStrapi({ url: comment.avatar.url })
    }
    return generateAvatarFromInitials(comment.name || '', 56, {
      rounded: false,
    })
  }

  return (
    <div>
      <div className={cn('relative', getMarginClass())}>
        <div className="flex gap-3 mb-6">
          <div
            className={cn(
              'relative z-10 h-[50px] w-[56px] cursor-pointer overflow-hidden shrink-0',
            )}
            style={{
              clipPath:
                'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
            }}
          >
            <div
              className="absolute top-1/2 left-1/2 z-20 h-[50px] w-[56px] -translate-x-1/2 -translate-y-1/2 flex items-center justify-center"
              style={{ clipPath: 'inherit' }}
            >
              <Image
                src={getAvatarUrl()}
                alt={comment.name || 'User avatar'}
                width={56}
                height={50}
                className="h-full w-full object-cover"
              />
            </div>
          </div>
          <div className="flex-1 bg-white py-2 px-4 rounded-md">
            <div className="flex justify-between">
              <h4 className="font-medium text-gray-900">{comment.name}</h4>
              <span className="text-sm text-gray-400 font-medium">
                {formatDate(comment.createdAt || '')}
              </span>
            </div>
            <p className="text-gray-700 mt-1">{comment.content}</p>
            <div className="flex items-center gap-4 mt-2">
              <LikeButton initialCount={comment.count_like || 0} />
              {currentLevel < maxLevel && (
                <button
                  className="text-sm text-yellow-500 flex items-center gap-1 hover:text-yellow-500 cursor-pointer"
                  onClick={() => setShowReplyInput(!showReplyInput)}
                  disabled={isSubmitting}
                >
                  <MessageSquare size={16} />
                  <span>{comment.children?.length || 0}</span>
                </button>
              )}
            </div>

            {showReplyInput && (
              <div className="mt-3">
                <CommentInput
                  onSubmit={handleAddReply}
                  placeholder="Viết phản hồi..."
                  buttonText="PHẢN HỒI"
                  isReply={true}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {comment.children && comment.children.length > 0 && (
        <div className="space-y-4">
          {comment.children.map((reply: any) => (
            <CommentBox
              key={reply.documentId}
              comment={reply}
              level={currentLevel + 1}
              onAddReply={onAddReply}
              isSubmitting={isSubmitting}
            />
          ))}
        </div>
      )}
    </div>
  )
}
