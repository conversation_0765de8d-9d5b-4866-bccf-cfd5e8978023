const allowedOrigins = [
  'https://admin.phuthaicat.com.vn',
  'https://beta.phuthaicat.com.vn',
  'https://phuthaicat.com.vn',
  // localhost
  'http://localhost:1337',
  'http://localhost:3001',
  'https://d841-2402-800-6314-6e48-8ca6-a3e3-484a-b294.ngrok-free.app',
]

export default [
  'strapi::logger',
  'strapi::errors',
  'strapi::security',
  {
    name: 'strapi::cors',
    config: {
      origin: allowedOrigins,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
      headers: [
        'Content-Type',
        'Authorization',
        'Origin',
        'Accept',
        'Access-Control-Allow-Origin',
        'Access-Control-Request-Headers',
        'Access-Control-Allow-Headers',
        'Cache-Control', // Add 'Cache-Control' to the allowed headers
      ],
      keepHeaderOnError: true,
    },
  },
  {
    name: 'strapi::poweredBy',
    config: {
      enabled: true,
      poweredBy: 'PTC',
    },
  },
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
  'global::deepPopulate',
]
