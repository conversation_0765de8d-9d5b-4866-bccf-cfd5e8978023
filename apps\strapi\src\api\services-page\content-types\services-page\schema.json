{"kind": "singleType", "collectionName": "services_pages", "info": {"singularName": "services-page", "pluralName": "services-pages", "displayName": "/services"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.video-section", "dynamic-zone.solution-portfolio-section", "dynamic-zone.page-hero-section", "dynamic-zone.media-text-section", "dynamic-zone.image-slider-section", "dynamic-zone.image-box-section", "dynamic-zone.service-list-section", "dynamic-zone.icon-box-list-section", "dynamic-zone.info-block", "dynamic-zone.widget"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}}}