{"kind": "collectionType", "collectionName": "article_comments", "info": {"singularName": "article-comment", "pluralName": "article-comments", "displayName": "Article-Comment"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "content": {"type": "text"}, "avatar": {"type": "media", "multiple": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "is_guess": {"type": "boolean", "default": true}, "count_like": {"type": "integer", "min": 0}, "article": {"type": "relation", "relation": "manyToOne", "target": "api::article.article", "inversedBy": "article_comments"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::article-comment.article-comment", "mappedBy": "parent"}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::article-comment.article-comment", "inversedBy": "children"}}}