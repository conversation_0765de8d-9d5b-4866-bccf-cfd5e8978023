'use client'

import { SectionTitle } from '@ttplatform/core-page-builder/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Typography,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import { useRef, useState } from 'react'
//---------------------------------------------------------------------------------
const __LIST = [
  {
    title: 'VẬN HÀNH ĐƠN GIẢN',
    image: '/images/products/feature-1.png',
    contents: {
      image: '/images/products/feature-1.png',
      title: 'VẬN HÀNH ĐƠN GIẢN',
      description:
        'Office ipsum you must be muted. Feed angel disband game alpha respectively bells plan monday. Files three live here hiring please third. ',
    },
  },
  {
    title: 'CHI PHÍ SỞ HỮU & VẬN HÀNH THẤP',
    image: '/images/products/feature-2.png',
    contents: {
      image: '/images/products/feature-2.png',
      title: 'CHI PHÍ SỞ HỮU & VẬN HÀNH THẤP',
      description:
        'Office ipsum you must be muted. Feed angel disband game alpha respectively bells plan monday. Files three live here hiring please third. ',
    },
  },
  {
    title: 'BẢO DƯỠNG DỄ DÀNG',
    image: '/images/products/feature-3.png',
    contents: {
      image: '/images/products/feature-3.png',
      title: 'BẢO DƯỠNG DỄ DÀNG',
      description:
        'Office ipsum you must be muted. Feed angel disband game alpha respectively bells plan monday. Files three live here hiring please third. ',
    },
  },
  {
    title: 'VẬN HÀNH ĐƠN GIẢN',
    image: '/images/products/feature-1.png',
    contents: {
      image: '/images/products/feature-1.png',
      title: 'VẬN HÀNH ĐƠN GIẢN',
      description:
        'Office ipsum you must be muted. Feed angel disband game alpha respectively bells plan monday. Files three live here hiring please third. ',
    },
  },
  {
    title: 'CHI PHÍ SỞ HỮU & VẬN HÀNH THẤP',
    image: '/images/products/feature-2.png',
    contents: {
      image: '/images/products/feature-2.png',
      title: 'CHI PHÍ SỞ HỮU & VẬN HÀNH THẤP',
      description:
        'Office ipsum you must be muted. Feed angel disband game alpha respectively bells plan monday. Files three live here hiring please third. ',
    },
  },
]
//---------------------------------------------------------------------------------
export default function DetailsOutstandingFeatures() {
  const plugin = useRef(Autoplay({ delay: 2000, stopOnInteraction: true }))
  return (
    <section id="TINH_NANG">
      <div className="flex flex-col gap-10 py-10 lg:py-20">
        <div className="w-full container max-w-screen-xl mx-auto px-4">
          <SectionTitle text="TÍNH NĂNG NỔI BẬT" align="center" />
        </div>
        <div className="w-full pl-4 sm:pl-10 md:pl-20 lg:pl-[160px]">
          <Carousel
            plugins={[plugin.current]}
            opts={{
              align: 'start',
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent>
              {__LIST.map((item: any, idx: number) => (
                <CarouselItem
                  key={idx}
                  className="pl-4 md:pl-6 basis-3/4 md:basis-3/7 lg:basis-3/10"
                >
                  <Item item={item} />
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </section>
  )
}
//---------------------------------------------------------------------------------
type ItemProps = {
  item: any
}
function Item({ item }: ItemProps) {
  const [openDialog, setOpenDialog] = useState(false)
  return (
    <>
      <div
        onClick={() => setOpenDialog(true)}
        className="relative z-10 w-full h-auto aspect-[6/9] flex items-end rounded-2xl overflow-hidden cursor-pointer"
        style={{
          backgroundImage: `url(${item?.image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      >
        <div
          className="absolute z-20 w-full h-[40%] bottom-0"
          style={{
            background:
              'linear-gradient(0deg, #222 0%, rgba(34, 34, 34, 0.00) 100%)',
          }}
        ></div>
        <div className="relative z-30 p-5 md:p-8 lg:p-10">
          <Typography variant="h3" className="text-white">
            {item?.title}
          </Typography>
        </div>
      </div>
      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent className="max-w-[1024px]">
          <DialogHeader>
            <DialogTitle>Edit profile</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-4">
            <div
              className="w-full h-auto aspect-[16/9]"
              style={{
                backgroundImage: `url(${item?.contents?.image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
              }}
            />
            <Typography variant="h3" className="text-[#222222]">
              {item?.contents?.title}
            </Typography>
            <Typography variant="body1" className="text-[#222222]">
              {item?.contents?.description}
            </Typography>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
