'use client'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import Lenis from 'lenis'
import { JSX, useEffect, useRef } from 'react'

// Register GSAP ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger)

interface SmoothScrollContainerProps {
  children: React.ReactNode
  type?: 'one' | 'two' // Prop to select version; can be extended (e.g., "three")
}

// Define types for animation and render handlers
type AnimationHandler = (container: HTMLDivElement) => () => void // Handles animation logic and returns cleanup function
type RenderHandler = (
  children: React.ReactNode,
  containerRef: React.RefObject<HTMLDivElement | null>,
) => JSX.Element // Handles rendering JSX

// Version 1 (GSAP): Animation logic
const handleAnimationOne: AnimationHandler = (container) => {
  // Disable GSAP lag smoothing for smoother animations
  gsap.ticker.lagSmoothing(0)

  // Select all <section> elements inside the container
  const sections = container.querySelectorAll('section')

  // Apply GSAP animation to the first section: move up -120vh
  gsap.fromTo(
    sections[0],
    { y: 0, opacity: 1 }, // Initial state
    {
      y: '-60vh', // Move up
      opacity: 1,
      ease: 'power1.out', // Easing for smooth motion
      scrollTrigger: {
        trigger: sections[0], // Trigger animation on first section
        start: 'top top', // Start when top of section hits top of viewport
        end: 'bottom top+=0%', // End when bottom of section is 50% above viewport
        scrub: 1.5, // Smooth scrubbing
        markers: false, // Disable debug markers
      },
    },
  )

  // Cleanup: Kill all ScrollTriggers to prevent memory leaks
  return () => {
    ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
  }
}

// Version 1 (GSAP): Render logic
const renderOne: RenderHandler = (children, containerRef) => (
  <div ref={containerRef}>{children}</div>
)

// Version 2 (ReactLenis): Animation logic
const handleAnimationTwo: AnimationHandler = () => {
  // No custom animation needed; Lenis handles smooth scrolling
  return () => {} // Return empty cleanup function
}

// Version 2 (ReactLenis): Render logic
const renderTwo: RenderHandler = (children, containerRef) => {
  return (
    <div ref={containerRef} className="relative bg-gray-100">
      {children}
    </div>
  )
}

// Map `type` to corresponding animation and render handlers
const versionHandlers: Record<
  string,
  { animation: AnimationHandler; render: RenderHandler }
> = {
  one: { animation: handleAnimationOne, render: renderOne },
  two: { animation: handleAnimationTwo, render: renderTwo },
  // Add new versions here, e.g.:
  // three: { animation: handleAnimationThree, render: renderThree },
}

// Main component
export const SmoothScrollContainer = ({
  children,
  type = 'one',
}: SmoothScrollContainerProps) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (type === 'two') {
      const lenis = new Lenis()
      function raf(time: number) {
        lenis.raf(time)
        requestAnimationFrame(raf)
      }
      requestAnimationFrame(raf)
      return () => lenis.destroy()
    }

    const handler = versionHandlers[type]?.animation
    if (!handler || !containerRef.current) return
    const cleanup = handler(containerRef.current)
    return cleanup
  }, [type])

  const render = versionHandlers[type]?.render || renderOne
  return render(children, containerRef)
}
