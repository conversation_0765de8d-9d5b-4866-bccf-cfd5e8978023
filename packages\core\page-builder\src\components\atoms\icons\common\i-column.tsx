import React from 'react'

interface ColumnIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

export const ColumnIcon: React.FC<ColumnIconProps> = ({
  size = 20,
  className,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M4.8 1H4.2C3.0799 1 2.51984 1 2.09202 1.21799C1.71569 1.40973 1.40973 1.71569 1.21799 2.09202C1 2.51984 1 3.07989 1 4.2V15.8C1 16.9201 1 17.4802 1.21799 17.908C1.40973 18.2843 1.71569 18.5903 2.09202 18.782C2.51984 19 3.0799 19 4.2 19H4.8C5.9201 19 6.48016 19 6.90798 18.782C7.28431 18.5903 7.59027 18.2843 7.78201 17.908C8 17.4802 8 16.9201 8 15.8V4.2C8 3.0799 8 2.51984 7.78201 2.09202C7.59027 1.71569 7.28431 1.40973 6.90798 1.21799C6.48016 1 5.9201 1 4.8 1Z"
        stroke="#182230"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.8 1H15.2C14.0799 1 13.5198 1 13.092 1.21799C12.7157 1.40973 12.4097 1.71569 12.218 2.09202C12 2.51984 12 3.0799 12 4.2V15.8C12 16.9201 12 17.4802 12.218 17.908C12.4097 18.2843 12.7157 18.5903 13.092 18.782C13.5198 19 14.0799 19 15.2 19H15.8C16.9201 19 17.4802 19 17.908 18.782C18.2843 18.5903 18.5903 18.2843 18.782 17.908C19 17.4802 19 16.9201 19 15.8V4.2C19 3.0799 19 2.51984 18.782 2.09202C18.5903 1.71569 18.2843 1.40973 17.908 1.21799C17.4802 1 16.9201 1 15.8 1Z"
        stroke="#182230"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
