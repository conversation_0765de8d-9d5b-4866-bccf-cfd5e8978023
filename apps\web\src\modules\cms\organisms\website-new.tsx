'use client'

import { Button, Typography } from '@ttplatform/ui/components'

export default function WebsiteList() {
  const handleAddNewSite = () => {
    console.log('TODO: Add new site')
  }

  const renderHeader = (
    <div className="flex flex-row justify-between w-full">
      <Typography variant="h3">Your Websites</Typography>
      <Button
        color="info"
        variant="secondary"
        onClick={handleAddNewSite}
        size="lg"
      >
        + New Website
      </Button>
    </div>
  )

  const renderForm = (
    <div className="flex flex-col w-full">
      <div className="flex flex-row justify-between w-full">
        <Typography variant="h3">Create new website</Typography>
      </div>
    </div>
  )

  return (
    <>
      {renderHeader}

      {renderForm}
    </>
  )
}
