'use client'

import { Button, Typography } from '@ttplatform/ui/components'

export default function PostList() {
  const handleAddNewPost = () => {
    alert('TODO: Add new post')
  }

  const renderHeader = (
    <div className="flex flex-row justify-between w-full">
      <Typography variant="h4">Your Posts</Typography>
      <Button color="info" variant="secondary" onClick={handleAddNewPost}>
        + New Post
      </Button>
    </div>
  )

  const renderList = (
    <div className="flex flex-col w-full">
      <div className="flex flex-row justify-between w-full">
        <Typography variant="h3">List of posts</Typography>
      </div>
    </div>
  )

  return (
    <>
      {renderHeader}

      {renderList}
    </>
  )
}
