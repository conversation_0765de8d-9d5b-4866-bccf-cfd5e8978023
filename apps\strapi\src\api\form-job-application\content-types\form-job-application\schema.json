{"kind": "collectionType", "collectionName": "form_job_applications", "info": {"singularName": "form-job-application", "pluralName": "form-job-applications", "displayName": "Form Job-Application"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "email": {"type": "email"}, "phone": {"type": "string"}, "resume_link": {"type": "string"}, "cover_letter_link": {"type": "string"}, "career": {"type": "relation", "relation": "manyToOne", "target": "api::career.career", "inversedBy": "form_job_applications"}, "state": {"type": "enumeration", "default": "NEW", "enum": ["NEW", "CONTACTED", "REJECTED"]}, "metadata": {"type": "blocks"}}}