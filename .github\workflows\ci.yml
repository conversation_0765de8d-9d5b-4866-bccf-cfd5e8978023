name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run tests
        run: bun test

      - name: Run linting
        run: bun lint

      - name: Check formatting
        run: bun format --check

      - name: Build
        run: bun build

      - name: Run database migrations
        run: bun db:migrate:deploy
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Run database migrations
        run: bun db:migrate:deploy
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
