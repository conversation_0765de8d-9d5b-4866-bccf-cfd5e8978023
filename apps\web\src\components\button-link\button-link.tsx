import { Button, type ButtonProps } from '@ttplatform/ui/components'
import NextLink, { LinkProps } from 'next/link'

type T_ButtonLink = LinkProps &
  ButtonProps & {
    text?: string
    href: string
    children?: React.ReactNode
    locale?: string
  }

export default function ButtonLink({
  text,
  href,
  children,
  locale = 'en',
  ...props
}: T_ButtonLink) {
  const path = href.includes(locale) ? href : `/${locale}${href}`

  return (
    <NextLink href={path} locale={locale} passHref>
      <Button {...props}>
        {text}
        {children}
      </Button>
    </NextLink>
  )
}
