'use client'

import { cn } from '@ttplatform/ui/lib'
import { ArrowDown, ArrowLeft, ArrowRight, ArrowUp } from 'lucide-react'
import Link from 'next/link'
import { HTMLAttributeAnchorTarget } from 'react'
import { HexagonIcon } from '../..'
import { IButton } from '../../../libs/models'
import {
  ButtonStyleMapping,
  TButtonStylePreset,
} from '../../../libs/models/presets'
import {
  getButtonClass,
  mapComponentStyles,
} from '../../../libs/utils/style-mapper'
import { HexagonShadowIcon } from '../icons/common/i-hexagon-shadow'

interface MainButtonProps extends IButton {
  isDisabledIcon?: boolean
  onClick?: () => void
  className?: string
  boxIconColor?: string
  arrowIconColor?: string
  arrowDirection?: 'right' | 'left' | 'up' | 'down'
  type?: 'button' | 'submit' | 'reset'
  disabled?: boolean
  openInNewTab?: boolean
  styles?: Record<string, any>
  variant?: TButtonStylePreset
  CustomIcon?: any
  iconPosition?: 'left' | 'right'
}

const createRippleEffect = (event: React.MouseEvent<HTMLElement>): void => {
  const target = event.currentTarget
  const existingRipples = target.querySelectorAll('.ripple')
  existingRipples.forEach((ripple) => ripple.remove())

  const ripple = document.createElement('span')
  const diameter = Math.max(target.clientWidth, target.clientHeight)
  const radius = diameter / 2
  const rect = target.getBoundingClientRect()
  const x = event.clientX - rect.left - radius
  const y = event.clientY - rect.top - radius

  ripple.style.width = ripple.style.height = `${diameter}px`
  ripple.style.left = `${x}px`
  ripple.style.top = `${y}px`
  ripple.className = cn(
    'absolute bg-zinc-100/60 rounded-full pointer-events-none animate-ripple scale-100 ripple',
  )
  target.appendChild(ripple)

  ripple.addEventListener('animationend', () => ripple.remove())
}

const ArrowIcon: React.FC<{
  direction?: 'right' | 'left' | 'up' | 'down'
  color?: string
}> = ({ direction, color }) => {
  const iconProps = { size: 16, color }
  switch (direction) {
    case 'right':
      return <ArrowRight {...iconProps} />
    case 'left':
      return <ArrowLeft {...iconProps} />
    case 'up':
      return <ArrowUp {...iconProps} />
    case 'down':
      return <ArrowDown {...iconProps} />
    default:
      return null
  }
}

const HexIconWrapper: React.FC<{
  isIconOnly: boolean
  boxIconColor?: string
  arrowIconColor?: string
  arrowDirection?: 'right' | 'left' | 'up' | 'down'
  CustomIcon?: any
}> = ({
  isIconOnly,
  boxIconColor,
  arrowIconColor,
  arrowDirection,
  CustomIcon,
}) => {
  return (
    <div className="relative flex items-center justify-center shrink-0">
      <div
        className={cn('relative transition-opacity duration-300 ease-in-out')}
      >
        {isIconOnly ? (
          <HexagonShadowIcon
            size={44}
            color={boxIconColor}
            className={cn('opacity-100 group-hover:opacity-0')}
          />
        ) : (
          <HexagonIcon
            size={36}
            color={boxIconColor}
            className={cn('opacity-100')}
          />
        )}
        <div
          className={cn(
            'absolute left-1/2 -translate-x-1/2 transition-opacity duration-300 ease-in-out',
            isIconOnly ? 'top-[9px]' : 'top-1/2 -translate-y-1/2',
          )}
        >
          {CustomIcon ? (
            <CustomIcon size={16} />
          ) : (
            <ArrowIcon direction={arrowDirection} color={arrowIconColor} />
          )}
        </div>
      </div>
    </div>
  )
}

const MainButton = ({
  label,
  variant = 'primary',
  isDisabledIcon = false,
  styles = {},
  onClick,
  className,
  url,
  target = '_self',
  openInNewTab,
  boxIconColor = ButtonStyleMapping[variant].box_icon_color,
  arrowIconColor,
  arrowDirection = 'right',
  type = 'button',
  disabled = false,
  CustomIcon,
  iconPosition = 'right',
}: MainButtonProps) => {
  const isIconOnly = !label

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!disabled) {
      createRippleEffect(event)
      onClick?.()
    }
  }

  // Determine padding based on icon position
  const getPaddingClasses = () => {
    if (isDisabledIcon) return ''
    if (iconPosition === 'left') {
      return '!pl-[6px] !pr-[22px]' // Icon on left: less padding left, more padding right
    }
    return '!pl-[22px] !pr-[6px]' // Icon on right: more padding left, less padding right
  }

  const commonClassNames = cn(
    'group flex w-fit h-11 items-center gap-3 transition-all duration-300 ease-in-out uppercase outline-none relative overflow-hidden shrink-0 cursor-pointer justify-between',
    getPaddingClasses(),
    isIconOnly || !label || label.length === 0
      ? '!bg-transparent !p-0'
      : 'rounded-md shadow-[0px_16px_28.8px_-4.5px_#00000008,0px_5.11px_9.19px_-4px_#00000030,0px_1.93px_3.48px_-2.25px_#0000003D,0px_0.64px_1.15px_-1.13px_#00000042]',
    'hover:brightness-95 group',
    getButtonClass(variant),
    mapComponentStyles(styles, undefined, variant),
    disabled && 'opacity-50 cursor-not-allowed',
    className,
  )

  const renderIcon = () => {
    if (isDisabledIcon) return null

    const transformClass =
      iconPosition === 'left'
        ? 'transition-transform duration-300 group-hover:-translate-x-1'
        : 'transition-transform duration-300 group-hover:translate-x-1'

    return (
      <div className={transformClass}>
        <HexIconWrapper
          isIconOnly={isIconOnly}
          boxIconColor={boxIconColor}
          arrowIconColor={arrowIconColor}
          arrowDirection={arrowDirection}
          CustomIcon={CustomIcon}
        />
      </div>
    )
  }

  const buttonContent = (
    <>
      {iconPosition === 'left' && renderIcon()}
      {label && (
        <span className="text-sm sm:text-base font-bold whitespace-nowrap">
          {label}
        </span>
      )}
      {iconPosition === 'right' && renderIcon()}
    </>
  )

  if (url) {
    return (
      <Link
        href={url}
        target={openInNewTab ? '_blank' : (target as HTMLAttributeAnchorTarget)}
        className={commonClassNames}
        onClick={handleClick}
      >
        {buttonContent}
      </Link>
    )
  }

  return (
    <button
      onClick={handleClick}
      className={commonClassNames}
      type={type}
      disabled={disabled}
      aria-disabled={disabled}
    >
      {buttonContent}
    </button>
  )
}

export default MainButton
