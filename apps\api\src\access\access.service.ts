import { Injectable } from '@nestjs/common'
import { PrismaService } from '../modules/persistence/prisma/prisma.service'
import { CanAccessDTO } from './dto'

@Injectable()
export class AccessService {
  constructor(private readonly prisma: PrismaService) {}

  async canAccess(dto: CanAccessDTO): Promise<boolean> {
    const now = new Date()

    const user = await this.prisma.user.findUnique({
      where: { id: dto.user_id },
    })

    if (!user) {
      throw new Error('User not found')
    }

    const userRoles = await this.prisma.userRole.findMany({
      where: {
        user_id: user.id,
        valid_from: { lte: now },
        OR: [{ valid_to: null }, { valid_to: { gte: now } }],
      },
      include: {
        role: {
          include: {
            policies: {
              where: {
                is_active: true,
              },
            },
          },
        },
      },
    })

    if (!userRoles) return false

    return true
  }
}
