'use client'

import React from 'react'
import { StepConfig } from '../types/survey.types'

interface BaseStepProps {
  step: StepConfig
  children: React.ReactNode
  className?: string
}

export default function BaseStep({ children, className = '' }: BaseStepProps) {
  return (
    <div
      className={`w-full bg-gray-50 rounded-xl p-6 sm:p-10 border border-gray-20 text-gray-800 space-y-6 sm:space-y-10 ${className}`}
    >
      {/* Step Content */}
      {children}
    </div>
  )
}
