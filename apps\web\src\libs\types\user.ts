import { z } from 'zod'
import { T_DocumentReturn } from './common'

export type T_UserType = 'AGENT' | 'PERSONAL'

export const T_UserSchema = z.object({
  email: z.string().email(),
  username: z.string(),
  phone_number: z.string().optional(),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  type: z.enum(['AGENT', 'PERSONAL']),
  avatar: z.string().optional(),
})

export type T_UserSchema = z.infer<typeof T_UserSchema>

export type T_User = {
  username: string
  email: string
  phone_number: string
  first_name: string
  last_name: string
  type: T_UserType
  avatar: string
}

export type T_UserReturn = T_User & T_DocumentReturn
