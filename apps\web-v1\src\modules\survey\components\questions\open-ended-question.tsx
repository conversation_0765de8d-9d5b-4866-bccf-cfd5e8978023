'use client'

import { Input, Textarea, Typography } from '@ttplatform/ui/components'
import { motion } from 'framer-motion'
import React from 'react'
import { useSurveyValidation } from '../../hooks/use-survey-validation'
import { OpenEndedQuestion as OpenEndedQuestionType } from '../../types/survey.types'

interface OpenEndedQuestionProps {
  question: OpenEndedQuestionType
  value: string
  onChange: (value: string) => void
  error?: string
}

export default function OpenEndedQuestion({
  question,
  value = '',
  onChange,
  error,
}: OpenEndedQuestionProps) {
  const { clearQuestionError } = useSurveyValidation()

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const newValue = e.target.value

    // Clear error immediately when user starts interacting
    if (error) {
      clearQuestionError(question.id)
    }

    // Apply max length validation
    if (question.maxLength && newValue.length > question.maxLength) {
      return
    }

    onChange(newValue)
  }

  const isTextarea = question.maxLength && question.maxLength > 100
  const remainingChars = question.maxLength
    ? question.maxLength - value.length
    : null

  return (
    <motion.div
      className="space-y-3"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {isTextarea ? (
        <Textarea
          value={value}
          onChange={handleChange}
          placeholder={question.placeholder}
          className={`p-3 bg-white min-h-[100px] resize-vertical border border-gray-200${error ? 'border-destructive focus-visible:ring-destructive' : ''}`}
          rows={4}
          minLength={question.minLength}
          maxLength={question.maxLength}
        />
      ) : (
        <Input
          type="text"
          value={value}
          onChange={handleChange}
          placeholder={question.placeholder}
          className={`bg-white h-12 focus-visible:ring-primary ${error ? 'border-destructive focus-visible:ring-destructive' : ''}`}
          minLength={question.minLength}
          maxLength={question.maxLength}
        />
      )}

      {/* Character count */}
      {question.maxLength && (
        <div className="flex justify-between">
          <Typography variant="caption" className="text-muted-foreground">
            {question.minLength && <>Tối thiểu {question.minLength} ký tự</>}
          </Typography>
          <Typography
            variant="caption"
            className={
              remainingChars && remainingChars < 20
                ? 'text-destructive'
                : 'text-muted-foreground'
            }
          >
            {value.length}/{question.maxLength}
          </Typography>
        </div>
      )}

      {/* Length validation hint */}
      {question.minLength &&
        value.length > 0 &&
        value.length < question.minLength && (
          <Typography variant="caption" className="text-destructive">
            Vui lòng nhập ít nhất {question.minLength} ký tự
          </Typography>
        )}
    </motion.div>
  )
}
