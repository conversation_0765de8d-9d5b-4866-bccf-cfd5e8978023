import { ApiProperty } from '@nestjs/swagger'
import { PageLayout, PageStatus } from '@prisma/client'

export class Page {
  @ApiProperty()
  id: string

  @ApiProperty()
  title: string

  @ApiProperty()
  slug: string

  @ApiProperty({ required: false })
  content?: any

  @ApiProperty({ required: false })
  metadata?: any

  @ApiProperty()
  is_default: boolean

  @ApiProperty()
  is_hide_nav: boolean

  @ApiProperty()
  is_hide_footer: boolean

  @ApiProperty()
  is_hide_header: boolean

  @ApiProperty()
  is_hide_sidebar: boolean

  @ApiProperty({ enum: PageStatus })
  status: PageStatus

  @ApiProperty({ enum: PageLayout })
  page_layout: PageLayout

  @ApiProperty({ required: false })
  created_by?: string

  @ApiProperty()
  created_at: Date

  @ApiProperty()
  updated_at: Date

  @ApiProperty({ required: false })
  deleted_at?: Date
}
