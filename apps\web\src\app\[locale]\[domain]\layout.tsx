import { Suspense } from 'react'

import { cn } from '@ttplatform/ui/lib'
import { Montserrat } from 'next/font/google'

const montserrat = Montserrat({
  subsets: ['latin'],
  variable: '--font-montserrat',
})

export default async function Layout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html
      lang="en"
      data-mode="light"
      className={cn(montserrat.className, 'antialiased')}
    >
      <body>
        <Suspense fallback={<div>Loading...</div>}>
          <div className="flex flex-col gap-4 max-w-xl min-h-svh mx-auto ">
            {children}
          </div>
        </Suspense>
      </body>
    </html>
  )
}
