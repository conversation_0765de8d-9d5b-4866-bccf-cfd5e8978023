import {
  BCommitment,
  BContactBox,
  BFooterWithSchema,
  BHeaderWithSchema,
} from '@ttplatform/core-page-builder/components'

export async function generateMetadata() {
  return {
    title: 'Tech Solutions | PTC',
    description: 'Tech Solutions',
  }
}

import { _global, _mainNavigation, _topbar } from '../../../../../__mock__'

type TProps = {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}

export default async function TechSolutionLayout({ children, params }: TProps) {
  const { locale } = await params
  return (
    <>
      <BHeaderWithSchema
        header={_global.header as any}
        navigation={_mainNavigation as any}
        locale={locale}
        topbar={_topbar as any}
      />
      {children}
      <BContactBox />
      <BCommitment />
      <BFooterWithSchema data={_global.footer as any} />
    </>
  )
}
