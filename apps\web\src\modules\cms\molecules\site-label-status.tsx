import { cn, fDateTimeWithSeconds } from '@/utils'
import { I_SiteReturn } from '@ttplatform/common'
import { Typography } from '@ttplatform/ui/components'

type TProps = {
  website: I_SiteReturn
  className?: string
}

const SiteLabelStatus = ({ website, className }: TProps) => {
  const label = website.published_at
    ? `Last published on ${fDateTimeWithSeconds(website.published_at?.toString() ?? '')}`
    : `Created on ${fDateTimeWithSeconds(website.created_at?.toString() ?? '')}`

  return (
    <Typography
      variant="small"
      color="gray-600 "
      className={cn('font-bold', className)}
    >
      {label}
    </Typography>
  )
}

export default SiteLabelStatus
