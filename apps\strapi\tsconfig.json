{"compilerOptions": {"module": "CommonJS", "moduleResolution": "Node", "lib": ["ES2020"], "target": "ES2019", "strict": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "incremental": true, "esModuleInterop": true, "resolveJsonModule": true, "noEmitOnError": true, "noImplicitThis": true, "outDir": "dist", "rootDir": "."}, "include": ["./", "./**/*.ts", "./**/*.js", "src/**/*.json"], "exclude": ["node_modules/", "build/", "dist/", ".cache/", ".tmp/", "src/admin/", "**/*.test.*", "src/plugins/**"]}