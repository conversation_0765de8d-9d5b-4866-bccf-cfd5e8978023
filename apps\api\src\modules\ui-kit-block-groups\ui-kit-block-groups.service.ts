import { Injectable } from '@nestjs/common'
import { Prisma, UiKitBlockGroup } from '@prisma/client'
import { PaginatedResult, paginator } from 'prisma/utils/paginator'
import { PrismaService } from '../persistence/prisma/prisma.service'
import { CreateUiKitBlockGroupDto } from './dto/create-ui-kit-block-group.dto'
import { UpdateUiKitBlockGroupDto } from './dto/update-ui-kit-block-group.dto'
@Injectable()
export class UiKitBlockGroupsService {
  constructor(private readonly prisma: PrismaService) {}

  create(createUiKitBlockGroupDto: CreateUiKitBlockGroupDto) {
    return this.prisma.uiKitBlockGroup.create({
      data: {
        ...createUiKitBlockGroupDto,
        blocks: {
          create: createUiKitBlockGroupDto.blocks,
        },
      },
    })
  }

  async findAll(
    params: {
      skip?: number
      take?: number
      cursor?: Prisma.UiKitBlockGroupWhereUniqueInput
      where?: Prisma.UiKitBlockGroupWhereInput
      orderBy?: Prisma.UiKitBlockGroupOrderByWithRelationInput
      includeBlocks?: boolean
    } = {},
  ): Promise<PaginatedResult<UiKitBlockGroup>> {
    const { includeBlocks, skip, take = 10, cursor, where, orderBy } = params

    const include = {
      blocks: Boolean(includeBlocks?.toString() === 'true'),
    }

    const page = skip ? skip / take : 1

    return paginator(
      this.prisma.uiKitBlockGroup,
      {
        where,
        orderBy,
        include,
        cursor,
      },
      { page, take },
    )
  }

  findOne(id: string) {
    return this.prisma.uiKitBlockGroup.findUnique({ where: { id } })
  }

  update(id: string, updateUiKitBlockGroupDto: UpdateUiKitBlockGroupDto) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { blocks, ...rest } = updateUiKitBlockGroupDto

    return this.prisma.uiKitBlockGroup.update({
      where: { id },
      data: {
        ...rest,
      },
    })
  }

  remove(id: string) {
    return this.prisma.uiKitBlockGroup.delete({ where: { id } })
  }
}
