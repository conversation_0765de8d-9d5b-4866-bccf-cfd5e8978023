'use client'

import { useSession } from 'next-auth/react'
import { useEffect } from 'react'

// const PROTECTED_ROUTES = ['platform'];

type AuthGuardProps = {
  locale?: string
  children: React.ReactNode
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const { data: session } = useSession() as any // For this to work, the Page should be wrapped inside the SessionProvider component in Layout

  useEffect(() => {
    if (session?.error !== 'RefreshTokenError') return
    // signIn('credentials'); // Force sign in to obtain a new set of access and refresh tokens
  }, [session?.error])

  return children
}
