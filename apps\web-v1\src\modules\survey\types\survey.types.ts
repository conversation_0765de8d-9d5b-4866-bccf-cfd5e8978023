// Survey System Types - Dynamic and Extensible Architecture

export type QuestionType =
  | 'open-ended'
  | 'multiple-choice'
  | 'rating'
  | 'dropdown'

export type RatingLevel = 'satisfied' | 'neutral' | 'dissatisfied'

export interface BaseQuestion {
  id: string
  type: QuestionType
  title: string
  description?: string
  required: boolean
  order: number
}

export interface OpenEndedQuestion extends BaseQuestion {
  type: 'open-ended'
  placeholder?: string
  maxLength?: number
  minLength?: number
}

export interface MultipleChoiceOption {
  id: string
  label: string
  value: string
  hasOtherInput?: boolean
}

export interface MultipleChoiceQuestion extends BaseQuestion {
  type: 'multiple-choice'
  options: MultipleChoiceOption[]
  allowMultiple?: boolean
  hasOtherOption?: boolean
}

export interface DropdownQuestion extends BaseQuestion {
  type: 'dropdown'
  options: MultipleChoiceOption[]
  hasOtherOption?: boolean
}

export interface RatingQuestion extends BaseQuestion {
  type: 'rating'
  minValue: number
  maxValue: number
  showLabels?: boolean
  labels?: {
    min: string
    max: string
  }
  showFollowUpQuestion?: boolean
  followUpThreshold?: number // Show follow-up if rating <= this value
  followUpQuestion?: OpenEndedQuestion
  excludeFromFollowUp?: boolean // For question 1 as mentioned
}

export type SurveyQuestion =
  | OpenEndedQuestion
  | MultipleChoiceQuestion
  | DropdownQuestion
  | RatingQuestion

export interface StepConfig {
  id: string
  title: string
  subtitle?: string
  description?: string
  type: 'policy' | 'confirmation' | 'questions' | 'custom'
  order: number
  questions?: SurveyQuestion[]
  content?: string // For policy/confirmation steps
  buttons?: StepButton[]
  validation?: StepValidation
}

export interface StepButton {
  id: string
  label: string
  type: 'primary' | 'secondary' | 'danger'
  action: 'next' | 'previous' | 'submit' | 'cancel' | 'home'
  url?: string // For external navigation
  validation?: boolean // Whether to validate before action
}

export interface StepValidation {
  required?: boolean
  customValidation?: (data: any) => boolean | string
}

export interface SurveyConfig {
  id: string
  title: string
  description?: string
  steps: StepConfig[]
  settings: SurveySettings
  metadata?: SurveyMetadata
}

export interface SurveySettings {
  allowBackNavigation: boolean
  showProgressBar: boolean
  autoSave: boolean
  submitOnComplete: boolean
  redirectOnComplete?: string
}

export interface SurveyMetadata {
  createdAt: string
  updatedAt: string
  version: string
  locale: string
  tags?: string[]
}

// Survey State Management Types
export interface SurveyFormData {
  [questionId: string]: any
}

export interface SurveyState {
  config: SurveyConfig | null
  currentStepIndex: number
  formData: SurveyFormData
  isLoading: boolean
  errors: SurveyErrors
  isSubmitting: boolean
  isCompleted: boolean
  followUpQuestions: { [questionId: string]: boolean }
}

export interface SurveyErrors {
  [stepId: string]: {
    [questionId: string]: string
  }
}

export type SurveyAction =
  | { type: 'SET_CONFIG'; payload: SurveyConfig }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'NEXT_STEP' }
  | { type: 'PREVIOUS_STEP' }
  | { type: 'GO_TO_STEP'; payload: number }
  | { type: 'UPDATE_FORM_DATA'; payload: { questionId: string; value: any } }
  | { type: 'SET_ERRORS'; payload: SurveyErrors }
  | { type: 'CLEAR_ERRORS'; payload?: string }
  | { type: 'SET_SUBMITTING'; payload: boolean }
  | { type: 'SET_COMPLETED'; payload: boolean }
  | { type: 'TOGGLE_FOLLOW_UP'; payload: { questionId: string; show: boolean } }
  | { type: 'RESET_SURVEY' }

// Context Types
export interface SurveyContextType {
  state: SurveyState
  dispatch: React.Dispatch<SurveyAction>
  // Helper methods
  goToNextStep: () => void
  goToPreviousStep: () => void
  goToStep: (stepIndex: number) => void
  updateAnswer: (questionId: string, value: any) => void
  validateCurrentStep: () => boolean
  submitSurvey: () => Promise<void>
  resetSurvey: () => void
  getCurrentStep: () => StepConfig | null
  getProgress: () => number
  canGoNext: () => boolean
  canGoPrevious: () => boolean
}

// API Response Types
export interface SurveyApiResponse {
  data: SurveyConfig
  meta?: {
    pagination?: any
    [key: string]: any
  }
}

export interface SurveySubmissionData {
  surveyId: string
  responses: SurveyFormData
  metadata: {
    completedAt: string
    userAgent?: string
    locale?: string
    [key: string]: any
  }
}

// Utility Types
export type StepType = StepConfig['type']
export type QuestionValue<T extends SurveyQuestion> = T extends RatingQuestion
  ? number
  : T extends MultipleChoiceQuestion
    ? string | string[]
    : T extends DropdownQuestion
      ? string
      : T extends OpenEndedQuestion
        ? string
        : any

// Rating specific types
export interface RatingColors {
  satisfied: string
  neutral: string
  dissatisfied: string
}

export interface RatingConfig {
  colors: RatingColors
  thresholds: {
    satisfied: number // >= this value
    neutral: number // >= this value but < satisfied
    // dissatisfied is < neutral
  }
}
