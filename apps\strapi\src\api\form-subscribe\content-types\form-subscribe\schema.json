{"kind": "collectionType", "collectionName": "form_subscribes", "info": {"singularName": "form-subscribe", "pluralName": "form-subscribes", "displayName": "Form Subscribe"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "email": {"type": "string", "required": true}, "state": {"type": "enumeration", "default": "new", "enum": ["new", "answered", "closed"]}, "allow_send_email": {"type": "boolean", "default": true}, "metadata": {"type": "json"}}}