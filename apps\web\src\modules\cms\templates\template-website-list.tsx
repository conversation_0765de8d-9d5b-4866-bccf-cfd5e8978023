import { Skeleton } from '@ttplatform/ui/components'
import { AppContainer } from '@ttplatform/ui/templates'
import { Suspense } from 'react'
import { getSiteList } from '~/src/libs/data/cms-site'
import { WebsiteList } from '../organisms'

type TProps = {
  searchParams: Promise<{ page: string; take: string; query: string }>
}

export default async function TemplateWebsiteList({ searchParams }: TProps) {
  const { page, take, query } = (await searchParams) || {}

  const sites = await getSiteList({ page, take, query })

  return (
    <AppContainer>
      <Suspense fallback={<Skeleton className="h-full w-full" />}>
        <WebsiteList sites={sites} />
      </Suspense>
    </AppContainer>
  )
}
