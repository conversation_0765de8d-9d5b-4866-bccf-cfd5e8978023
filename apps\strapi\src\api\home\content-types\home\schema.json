{"kind": "singleType", "collectionName": "homes", "info": {"singularName": "home", "pluralName": "homes", "displayName": "/home"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"heading": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "subHeading": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.video-section", "dynamic-zone.testimonials-section", "dynamic-zone.related-products", "dynamic-zone.related-articles", "dynamic-zone.page-hero-section", "dynamic-zone.media-text-section", "dynamic-zone.locations-section", "dynamic-zone.latest-news-section", "dynamic-zone.hover-overlay-card-collection", "dynamic-zone.hover-expand-card-collection", "dynamic-zone.hero-slide-section", "dynamic-zone.form-subscribe", "dynamic-zone.features", "dynamic-zone.featured-promotions", "dynamic-zone.executive-team-section", "dynamic-zone.accordion-section", "dynamic-zone.features-section", "dynamic-zone.support-center-section", "dynamic-zone.stats-section", "dynamic-zone.service-list-section", "dynamic-zone.partners-commitment-section", "dynamic-zone.job-listings-section", "dynamic-zone.contact-us-section", "dynamic-zone.image-box-section", "dynamic-zone.image-slider-section", "dynamic-zone.widget"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "heading"}}}