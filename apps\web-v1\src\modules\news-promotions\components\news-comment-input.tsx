'use client'
import { Button, Input } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import Image from 'next/image'
import { useState } from 'react'
import { generateAvatarFromInitials } from '../../../utils/generate-avatar'

interface NewsCommentInputProps {
  onSubmit: (text: string, name: string) => void
  placeholder?: string
  buttonText?: string
  isSubmitting?: boolean
  disabled?: boolean
}

const NewsCommentInput = ({
  onSubmit,
  placeholder = 'Viết bình luận...',
  buttonText = 'ĐĂNG BÌNH LUẬN',
  isSubmitting = false,
  disabled = false,
}: NewsCommentInputProps) => {
  const [comment, setComment] = useState('')
  const [name, setName] = useState('')

  const handleSubmit = () => {
    if (comment.trim() && name.trim()) {
      onSubmit(comment, name)
      setComment('')
      setName('')
    }
  }

  const avatarUrl = generateAvatarFromInitials(name, 40, {
    background: '#E0E0E0',
  })

  return (
    <div className="space-y-4">
      <div className="flex gap-3 items-start w-full">
        <div
          className={cn(
            'relative z-10 h-[50px] w-[56px] cursor-pointer overflow-hidden shrink-0',
          )}
          style={{
            clipPath:
              'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
          }}
        >
          <div
            className="absolute top-1/2 left-1/2 z-20 h-[50px] w-[56px] -translate-x-1/2 -translate-y-1/2 flex items-center justify-center"
            style={{ clipPath: 'inherit' }}
          >
            <Image
              src={avatarUrl}
              alt="Avatar"
              className="h-full w-full object-cover"
              width={100}
              height={100}
              style={{ clipPath: 'inherit' }}
            />
          </div>
        </div>
        <div className="flex-1 flex flex-col gap-2">
          <Input
            className="w-full border rounded-md p-3 h-12 bg-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
            placeholder="Tên của bạn..."
            value={name}
            onChange={(e) => setName(e.target.value)}
            disabled={disabled}
          />
          <Input
            className="w-full border rounded-md p-3 h-12 bg-white focus:outline-none focus:ring-2 focus:ring-yellow-400 resize-none"
            placeholder={placeholder}
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            disabled={disabled}
          />
        </div>
        <Button
          onClick={handleSubmit}
          className="bg-yellow-400 hover:bg-yellow-500 text-black font-semibold h-12 disabled:opacity-50"
          disabled={disabled || isSubmitting || !comment.trim() || !name.trim()}
        >
          {isSubmitting ? 'Đang gửi...' : buttonText}
        </Button>
      </div>
    </div>
  )
}

export default NewsCommentInput
