// api-key.prisma

/// ///////////////////
/// ///////////////////
enum ApiKeyType {
  PUBLISHABLE
  SECRET
}

//////////////////////
// 🔑 API Key
//////////////////////
model ApiKey {
  id           String     @id @default(uuid())
  token        String
  salt         String
  redacted     String
  title        String
  type         ApiKeyType @default(PUBLISHABLE)
  last_used_at DateTime?
  created_by   String
  revoked_by   String?
  revoked_at   DateTime?
  created_at   DateTime   @default(now())
  updated_at   DateTime   @default(now())
  deleted_at   DateTime?

  @@index([deleted_at])
}

model AuditLog {
  id         String    @id @default(uuid())
  user_id    String?
  action     String
  model      String
  entity_id  String
  old_value  Json?
  new_value  Json?
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?

  @@index([deleted_at])
}
