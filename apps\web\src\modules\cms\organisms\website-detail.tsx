'use client'

import { I_SiteReturn } from '@ttplatform/common/src/libs/interfaces'
import { Button, Typography } from '@ttplatform/ui/components'
import { AppContainer } from '@ttplatform/ui/templates'
import Link from 'next/link'
import { paths } from '~/src/routes/paths'

type TProps = {
  details: I_SiteReturn
}

export default function WebsiteDetail({ details }: TProps) {
  const renderHeader = (
    <div className="flex flex-row justify-between w-full">
      <Typography variant="h3">{details.name}</Typography>
      <Link href={paths.app.cms.websites.root}>
        <Button color="info" variant="secondary" size="lg">
          Back to list
        </Button>
      </Link>
    </div>
  )

  const renderDetails = (
    <div className="flex flex-col w-full">
      <div className="flex flex-row justify-between w-full">
        <Typography variant="h3">Website details</Typography>
      </div>

      <div className="flex w-full">
        <div>{JSON.stringify(details)}</div>
      </div>
    </div>
  )

  return (
    <AppContainer>
      {renderHeader}

      {renderDetails}
    </AppContainer>
  )
}
