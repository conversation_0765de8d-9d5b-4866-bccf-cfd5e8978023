# Module: CMS

## Overview

This module provides a set of components and utilities for building websites.

## Structure

```bash
modules/
├── cms/
│ ├── index.ts                # Entry point for the module
│ ├── atoms/                  # Atoms: Basic building blocks (buttons, inputs, labels, etc. -- use shadcn/ui for this (@ttplatform/ui))
│ ├── molecules/              # Molecules: Reusable components (button groups, input groups, etc.)
│ ├── organisms/              # Organisms: Complex components (headers, footers, etc.)
│ └── templates/              # Templates (server components): Complete pages - (home, about, etc.)
```
