'use client'

import { SectionTitle } from '@ttplatform/core-page-builder/components'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Typography,
} from '@ttplatform/ui/components'
import { Minus, Plus } from 'lucide-react'
import { useState } from 'react'
//---------------------------------------------------------------------------------
type IProps = {
  product: any
}
const __FAQ = {
  image: '',
  list: [
    {
      question: 'Office ipsum you must be muted',
      answer: [
        'Genuine Cat parts improve the life and performance of Cat machines and engines',
        'Cat fluids are critical to support contamination control initiatives',
        'Cat improved industry fluids and standards to keep up with machine and engine design changes',
      ],
    },
    {
      question: 'Are Cat Fluids sustainable?',
      answer: [
        'Genuine Cat parts improve the life and performance of Cat machines and engines',
        'Cat fluids are critical to support contamination control initiatives',
        '<PERSON> improved industry fluids and standards to keep up with machine and engine design changes',
      ],
    },
    {
      question: 'How often should I change Cat Fluids in my equipment?',
      answer: [
        'Genuine Cat parts improve the life and performance of Cat machines and engines',
        'Cat fluids are critical to support contamination control initiatives',
        'Cat improved industry fluids and standards to keep up with machine and engine design changes',
      ],
    },
    {
      question: 'Best fluids for a specific type of engine?',
      answer: [
        'Genuine Cat parts improve the life and performance of Cat machines and engines',
        'Cat fluids are critical to support contamination control initiatives',
        'Cat improved industry fluids and standards to keep up with machine and engine design changes',
      ],
    },
    {
      question: 'Best fluids for a specific type of engine?',
      answer: [
        'Genuine Cat parts improve the life and performance of Cat machines and engines',
        'Cat fluids are critical to support contamination control initiatives',
        'Cat improved industry fluids and standards to keep up with machine and engine design changes',
      ],
    },
    {
      question: 'Best fluids for a specific type of engine?',
      answer: [
        'Genuine Cat parts improve the life and performance of Cat machines and engines',
        'Cat fluids are critical to support contamination control initiatives',
        'Cat improved industry fluids and standards to keep up with machine and engine design changes',
      ],
    },
  ],
}
//---------------------------------------------------------------------------------
export default function DetailsFaq({ product }: IProps) {
  const [open, setOpen] = useState<number | null>(0)
  return (
    <section className="bg-gray-50">
      <div className="w-full container max-w-screen-xl mx-auto px-4">
        <div className="flex flex-col gap-10 py-10 lg:py-20">
          <SectionTitle
            text={`Khách hàng thường hỏi về ${product?.product_code}`}
            align="center"
          />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            <Accordion type="single" collapsible defaultValue={`${0}`}>
              {__FAQ.list.map((item: any, idx) => (
                <AccordionItem key={idx} value={`${idx}`}>
                  <AccordionTrigger
                    className="[&>svg]:hidden"
                    onClick={() => setOpen(idx)}
                  >
                    <div className="w-full flex items-start justify-between gap-2">
                      <Typography
                        variant="body1"
                        className="text-gray-800 font-semibold"
                      >
                        {idx + 1}.&nbsp;{item.question}
                      </Typography>
                      {open == idx ? (
                        <Minus className="text-gray-800 min-w-6 min-h-6 transition-all duration-300" />
                      ) : (
                        <Plus className="text-gray-800 min-w-6 min-h-6 transition-all duration-300" />
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="flex flex-col gap-2">
                      {item.answer.map((answer: any, idx: number) => (
                        <Typography
                          variant="body1"
                          key={idx}
                          className="text-gray-800"
                        >
                          {answer}
                        </Typography>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
            <div
              className="w-full h-full min-h-[300px]"
              style={{
                backgroundImage: 'url(/images/products/faq.png)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
              }}
            ></div>
          </div>
        </div>
      </div>
    </section>
  )
}
