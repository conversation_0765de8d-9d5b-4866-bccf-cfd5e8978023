import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  Typography,
} from '@ttplatform/ui/components'
import { Calendar1, MapPin, Search, UserRound } from 'lucide-react'
import Link from 'next/link'
import { useCallback } from 'react'

// ---------------------------------------------------------------------------------
const _CATEGORY_LIST = [
  {
    id: 1,
    slug: 'bo-phan-ky-thuat-e-t',
    name: 'bộ phận kỹ thuật E&T',
  },
  {
    id: 2,
    slug: 'phong-kinh-doanh-may-phat-dien',
    name: '<PERSON>òng kinh doanh máy phát điện',
  },
  {
    id: 3,
    slug: 'phong-kinh-doanh-may-cong-trinh',
    name: '<PERSON>òng kinh doanh máy công trình',
  },
  {
    id: 4,
    slug: 'phong-kinh-doanh-may-thuy',
    name: 'Phòng kinh doanh máy thuỷ',
  },
]
const _MOCK_DATA = [
  {
    title: 'KỸ SƯ DỊCH VỤ MÁY PHÁT ĐIỆN – CƠ KHÍ',
    slug: 'ky-su-dich-vu-may-phat-dien-co-khi',
    category: {
      id: 1,
      slug: 'bo-phan-ky-thuat-e-t',
      name: 'bộ phận kỹ thuật E&T',
    },
    workingForm: 'full-time',
    position: 'Bộ phận kỹ thuật E&T',
    location: {
      id: 1,
      slug: 'ha-noi',
      name: 'Hà Nội',
    },
    quantity: 4,
    deadline: '30 June 2025',
  },
  {
    title: 'KỸ SƯ KINH DOANH MÁY PHÁT ĐIỆN',
    slug: 'ky-su-kinh-doanh-may-phat-dien',
    category: {
      id: 2,
      slug: 'phong-kinh-doanh-may-phat-dien',
      name: 'Phòng kinh doanh máy phát điện',
    },
    workingForm: 'full-time',
    position: 'Phòng kinh doanh máy phát điện',
    location: {
      id: 1,
      slug: 'ha-noi',
      name: 'Hà Nội',
    },
    quantity: 4,
    deadline: '30 June 2025',
  },
  {
    title: 'NHÂN VIÊN KINH DOANH MÁY CÔNG TRÌNH',
    slug: 'nhan-vien-kinh-doanh-may-cong-trinh',
    category: {
      id: 3,
      slug: 'phong-kinh-doanh-may-cong-trinh',
      name: 'Phòng kinh doanh máy công trình',
    },
    workingForm: 'full-time',
    position: 'Phòng kinh doanh máy công trình',
    location: {
      id: 1,
      slug: 'ha-noi',
      name: 'Hà Nội',
    },
    quantity: 4,
    deadline: '30 June 2025',
  },
  {
    title: 'SALES ENGINEER - GOVERNMENTAL',
    slug: 'sales-engineer-governmental',
    category: {
      id: 4,
      slug: 'phong-kinh-doanh-may-thuy',
      name: 'Phòng kinh doanh máy thuỷ',
    },
    workingForm: 'full-time',
    position: 'Phòng kinh doanh máy thủy',
    location: {
      id: 1,
      slug: 'ha-noi',
      name: 'Hà Nội',
    },
    quantity: 4,
    deadline: '30 June 2025',
  },
]
// ---------------------------------------------------------------------------------
const _SORT_LIST = [
  {
    id: 1,
    slug: 'newest',
    name: 'Mới nhất',
  },
]
// ---------------------------------------------------------------------------------
type FilterResultsProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}
// ---------------------------------------------------------------------------------
export default function FilterResults({
  // filters,
  onFilters,
}: FilterResultsProps) {
  const handleSort = useCallback(
    (slug: string) => {
      onFilters('sort', slug)
    },
    [onFilters],
  )

  const renderFilterAndSort = (
    <div className="flex items-center justify-between gap-6">
      <div
        className="max-w-[450px] min-w-[150px] w-full px-2.5 border border-gray-200 rounded-lg flex items-center gap-2"
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        <Search className="w-5 h-5 min-w-5 text-gray-500" />
        <Input placeholder="Tìm tin tuyển dụng" className="border-none" />
        {/* <ButtonMain /> */}
      </div>
      <Select onValueChange={handleSort} defaultValue={_SORT_LIST[0]?.slug}>
        <SelectTrigger className="">
          <SelectValue placeholder="Chọn tỉnh/thành phố" />
        </SelectTrigger>
        <SelectContent>
          {_SORT_LIST.map((item: any, idx: number) => (
            <SelectItem key={idx} value={item?.slug}>
              {item?.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
  return (
    <div className="flex flex-col gap-4">
      {renderFilterAndSort}
      <div className="flex flex-col gap-10">
        {_CATEGORY_LIST.map((category: any, idx: number) => {
          const data = _MOCK_DATA.filter(
            (item: any) => item.category.slug === category.slug,
          )
          return (
            <Accordion
              key={idx}
              type="single"
              collapsible
              defaultValue={category?.slug}
            >
              <AccordionItem value={category?.slug}>
                <AccordionTrigger
                  className="px-6 py-4"
                  style={{
                    background:
                      'linear-gradient(90deg, #FC0 73.08%, #FFD326 87.02%, #FFDE59 100%)',
                  }}
                >
                  <Typography variant="h5" className="text-gray-800 font-bold">
                    {category?.name}
                  </Typography>
                </AccordionTrigger>
                <AccordionContent className="pt-6">
                  <div className="grid grid-cols-3 gap-6">
                    {data?.map((item: any, idx: number) => (
                      <CareerItem item={item} key={idx} />
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )
        })}
      </div>
    </div>
  )
}
// ---------------------------------------------------------------------------------
function CareerItem({ item }: { item: any }) {
  const DetailsItem = ({
    icon,
    title,
    description,
  }: {
    icon: any
    title: string
    description: string
  }) => {
    return (
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-1">
          {icon}
          <Typography variant="caption" className="text-gray-600 font-medium">
            {title}
          </Typography>
        </div>
        <Typography variant="caption" className="text-[#AF0E0E] font-medium">
          {description}
        </Typography>
      </div>
    )
  }

  return (
    <Link href={`/career/${item?.slug}`}>
      <div
        className="flex flex-col gap-6 p-6 bg-gray-50 rounded-lg border border-gray-200"
        style={{
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        }}
      >
        <div className="flex flex-col gap-2">
          <Typography variant="h6" className="text-[#182230] font-semibold">
            {item?.title}
          </Typography>
          <div className="flex items-center gap-2">
            <Typography
              variant="caption"
              className="text-[#FF9900] font-medium"
            >
              {item?.workingForm === 'full-time'
                ? 'Toàn thời gian'
                : 'Bán thời gian'}
            </Typography>
            <Separator orientation="vertical" className="bg-[#D0D5DD]" />
            <Typography
              variant="caption"
              className="text-[#FF9900] font-medium"
            >
              {item?.position}
            </Typography>
          </div>
        </div>
        <div className="flex flex-col gap-6">
          <DetailsItem
            icon={<UserRound className="w-5 h-5 min-w-5" />}
            title="Số lượng tuyển"
            description={item?.quantity}
          />
          <DetailsItem
            icon={<Calendar1 className="w-5 h-5 min-w-5" />}
            title="Hạn nộp hồ sơ"
            description={item?.deadline}
          />
          <DetailsItem
            icon={<MapPin className="w-5 h-5 min-w-5" />}
            title="Số lượng tuyển"
            description={item?.location?.name}
          />
        </div>
      </div>
    </Link>
  )
}
