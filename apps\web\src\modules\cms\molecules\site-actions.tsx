'use client'

import { useCallback, useState } from 'react'

import { useBoolean } from '@/libs/hooks/use-boolean'
import { I_SiteReturn } from '@ttplatform/common'
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
} from '@ttplatform/ui/components'
import { Edit, Home, Trash } from 'lucide-react'
import { toast } from 'sonner'
import { deleteSite, updateSite } from '~/src/libs/data/cms-site'

type TProps = {
  site: I_SiteReturn
  renderTrigger: React.ReactNode
  onSuccess?: () => void
}

export default function SiteActions({
  site,
  renderTrigger,
  onSuccess,
}: TProps) {
  const [websiteName, setWebsiteName] = useState(site.name)
  const [websiteNameDeleting, setWebsiteNameDeleting] = useState('')

  const openEditSiteName = useBoolean()
  const openDeleteSite = useBoolean()

  const handleSetPrimarySite = async (id: string, primarySiteId?: string) => {
    console.log('handleSetPrimarySite', id, primarySiteId)
  }

  const handleDeleteSite = async () => {
    try {
      await deleteSite(site.id, true)
      toast.success('Website deleted successfully')
      setWebsiteNameDeleting('')
      openDeleteSite.onFalse()
      onSuccess?.()
    } catch (error) {
      console.error(error)
      toast.error('Failed to delete website')
    }
  }

  const handleEditSiteName = useCallback(async () => {
    try {
      await updateSite(site.id, { name: websiteName })
      toast.success('Website name updated successfully')
      openEditSiteName.onFalse()
      onSuccess?.()
    } catch (error) {
      console.error(error)
      toast.error('Failed to update website name')
    }
  }, [websiteName, site.id, openEditSiteName, onSuccess])

  return (
    <>
      {/* Dropdown Menu */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          {/* @ts-ignore */}
          {renderTrigger}
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[220px] p-2">
          <DropdownMenuItem className="cursor-pointer">
            <div onClick={openEditSiteName.onTrue} className="flex gap-2">
              <Edit size={16} /> Rename
            </div>
          </DropdownMenuItem>
          {!site.is_primary && (
            <DropdownMenuItem
              onClick={() => {
                handleSetPrimarySite(site.id, site.id)
              }}
              className="cursor-pointer"
            >
              <Home size={16} /> Set as primary website
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={openDeleteSite.onTrue}
            className="cursor-pointer"
          >
            <div className="flex gap-2 text-red-500">
              <Trash size={16} className="text-red-500" />
              Delete Website
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Dialog edit site name */}
      <Dialog
        open={openEditSiteName.value}
        onOpenChange={openEditSiteName.setValue}
      >
        <DialogContent className="w-full max-w-md">
          <DialogHeader className="gap-4">
            <DialogTitle>Edit the website name</DialogTitle>
            <div>
              <Input
                value={websiteName}
                onChange={(e) => setWebsiteName(e.target.value)}
                placeholder="Enter your website name here"
              />
              {!websiteName.length && (
                <small className="text-xs pl-1 text-red-500">
                  Website name is required
                </small>
              )}
            </div>
            <div className="flex justify-between gap-2">
              <Button
                onClick={openEditSiteName.onFalse}
                variant="outline"
                color="white"
                size="lg"
                className="w-full"
              >
                Cancel
              </Button>
              <Button
                variant="secondary"
                color="info"
                size="lg"
                className="w-full"
                disabled={!websiteName.length || websiteName === site.name}
                onClick={handleEditSiteName}
              >
                Save
              </Button>
            </div>
          </DialogHeader>
        </DialogContent>
      </Dialog>

      {/* Dialog delete site */}
      <Dialog
        open={openDeleteSite.value}
        onOpenChange={openDeleteSite.setValue}
      >
        <DialogContent className="w-full max-w-md">
          <DialogHeader className="gap-4">
            <DialogTitle>Delete website?</DialogTitle>
            <DialogDescription>
              Deleting your website is irreversible. Confirm by typing your
              website name [<b>{site.name}</b>] below:
            </DialogDescription>
          </DialogHeader>
          <Input
            value={websiteNameDeleting}
            onChange={(e) => setWebsiteNameDeleting(e.target.value)}
            placeholder="Enter your website name here"
          />
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={openDeleteSite.onFalse}
              variant="ghost"
              color="white"
              className="w-full"
            >
              Cancel
            </Button>
            <Button
              variant="ghost"
              color="danger"
              disabled={websiteNameDeleting !== site.name}
              onClick={handleDeleteSite}
              className="w-full"
            >
              Delete website
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
