import { TemplateWebsiteDetail } from '@/modules/cms'
import { Globe, HomeIcon, SquareEqual } from 'lucide-react'
import { Metadata } from 'next'
import { AppInset } from '~/src/components/layouts'
import { paths } from '~/src/routes/paths'

export const metadata: Metadata = {
  title: 'Pages',
}

type TProps = {
  params: Promise<{ id: string }>
}

export default async function SiteDetailPage({ params }: TProps) {
  const { id } = await params

  return (
    <AppInset
      breadcrumb={{
        items: [
          {
            label: 'PTC',
            href: paths.app.root,
            icon: <HomeIcon />,
          },
          {
            label: 'Websites',
            href: paths.app.cms.websites.root,
            icon: <Globe />,
          },
          {
            label: 'List pages',
            href: paths.app.cms.websites.view(id),
            icon: <SquareEqual />,
            isActive: true,
          },
        ],
      }}
    >
      <TemplateWebsiteDetail params={params} />
    </AppInset>
  )
}
