// user.prisma

/// ///////////////////
/// ///////////////////

enum PolicyEffect {
  ALLOW
  DENY
}

/// ///////////////////
/// ///////////////////
model User {
  id             String      @id @default(cuid())
  name           String?
  email          String?     @unique
  email_verified DateTime?
  image          String?
  password       String?
  created_at     DateTime    @default(now())
  updated_at     DateTime    @updatedAt
  deleted_at     DateTime?
  accounts       Account[]
  sessions       Session[]
  roles          UserRole[]
  cms_domains    CmsDomain[]
  cms_sites      CmsSite[]

  @@index([deleted_at])
}

model Account {
  id                  String    @id @default(cuid())
  user_id             String
  type                String
  provider            String
  provider_account_id String
  refresh_token       String?
  access_token        String?
  expires_at          Int?
  token_type          String?
  scope               String?
  id_token            String?
  session_state       String?
  deleted_at          DateTime?
  user                User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([provider, provider_account_id])
  @@index([user_id])
  @@index([deleted_at])
}

model Session {
  id            String    @id @default(cuid())
  session_token String    @unique
  user_id       String
  expires       DateTime
  data          Json?
  deleted_at    DateTime?
  user          User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([deleted_at])
}

/// ///////////////////
/// ///////////////////
model Role {
  id          String     @id @default(cuid())
  name        String     @unique
  description String?
  created_at  DateTime   @default(now())
  updated_at  DateTime   @updatedAt
  deleted_at  DateTime?
  users       UserRole[]
  policies    Policy[]   @relation("PolicyToRole")

  @@index([deleted_at])
}

model Policy {
  id          String       @id @default(cuid())
  name        String       @unique
  description String?
  actions     String[]
  resources   String[]
  conditions  Json?
  priority    Int          @default(0)
  is_active   Boolean      @default(true)
  created_at  DateTime     @default(now())
  updated_at  DateTime     @updatedAt
  deleted_at  DateTime?
  effect      PolicyEffect @default(ALLOW)
  roles       Role[]       @relation("PolicyToRole")

  @@index([deleted_at])
  @@index([conditions], type: Gin)
  @@index([actions])
  @@index([resources])
}

model Resource {
  id          String    @id @default(cuid())
  name        String    @unique
  type        String
  description String?
  metadata    Json?
  attributes  Json?
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  deleted_at  DateTime?

  @@index([deleted_at])
  @@index([type])
  @@index([metadata], type: Gin)
  @@index([attributes], type: Gin)
}

model UserRole {
  user_id    String
  role_id    String
  context    Json?
  valid_from DateTime  @default(now())
  valid_to   DateTime?
  created_at DateTime  @default(now())
  deleted_at DateTime?
  role       Role      @relation(fields: [role_id], references: [id], onDelete: Cascade)
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@id([user_id, role_id])
  @@index([role_id])
  @@index([deleted_at])
  @@index([context], type: Gin)
  @@index([valid_from, valid_to])
}
