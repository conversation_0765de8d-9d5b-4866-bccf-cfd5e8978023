import {
  BGridTwoColumn,
  BHeroPage,
} from '@ttplatform/core-page-builder/components'
import CareerList from './career-list'
import RelatedPosts from './components/related-posts'
// ----------------------------------------------------------------------
const _HEADING = {
  title: 'TUYỂN DỤNG',
  description:
    "Office ipsum you must be muted. Kpis problem lot explore ideal identify it's out time. Manage let's exploratory lift an need. Eye floor web no all needle. Regroup leverage break able three bake asserts rehydrate creep just. Boil into initiative strategy principles win-win-win job with.",
  buttons: [],
  image: '/images/career/career-hero.png',
}

const _GRID_SECTION_1 = {
  heading: {
    title: 'Công ty phân phối thiết bị công nghiệp hàng đầu Việt Nam',
    align: 'left',
  },
  contents: [
    'Công ty TNHH Công Nghiệp Phú Thái là Đại lý <PERSON>h thức Duy nhất của Caterpillar tại Việt Nam với tên giao dịch là Phu Thai Cat. Caterpillar là tập đoàn sản xuất máy công trình & khai mỏ, máy phát điện, động cơ diesel, động cơ máy thủy và xe nâng hàng… lớn nhất thế giới.',
    'Phu Thai Cat không chỉ cung cấp các thiết bị mang thương hiệu Cat® mới 100% mà còn cho thuê và bán các thiết bị đã qua sử dụng. Phu Thai Cat cung cấp các dịch vụ hỗ trợ sau bán hàng như lắp đặt, vận hành và đưa ra các khuyến cáo, cung cấp phụ tùng thay thế chính hãng, dịch vụ sửa chữa, dịch vụ đào tạo, thực hiện công việc bảo hành và tư vấn các giải pháp cho các doanh nghiệp.',
    'Phu Thai Cat luôn luôn sẵn sàng hỗ trợ khách hàng 24/7 với đội ngũ hơn 400 cán bộ nhân viên nhiều năm kinh nghiệm cả về mặt quản lý cũng như vận hành, đội ngũ kỹ sư được đào tạo chuyên sâu, đội ngũ chuyên gia nước ngoài giàu kinh nghiệm tại trụ sở chính ở Hà Nội, văn phòng chi nhánh ở TP Hồ Chí Minh, 3 Trung tâm hỗ trợ sản phẩm tại Đồng Nai, Hưng Yên, Quảng Ninh và các trạm sửa chữa trên toàn quốc.',
  ],
  buttons: [],
  image: '/images/about-intro-image.png',
  isImageRight: true,
}
// ----------------------------------------------------------------------
export default function CareerView() {
  return (
    <div>
      <BHeroPage
        heading={_HEADING}
        styles={{}}
        image={_HEADING.image}
        video={null}
      />
      <BGridTwoColumn data={_GRID_SECTION_1} />
      <CareerList />
      <RelatedPosts />
    </div>
  )
}
