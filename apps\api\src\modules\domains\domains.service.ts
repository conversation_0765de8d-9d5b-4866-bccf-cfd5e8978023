import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { paginator } from 'prisma/utils/paginator'
import { PrismaService } from 'src/modules/persistence/prisma/prisma.service'
import { CreateDomainDto } from './dto/create-domain.dto'
import { UpdateDomainDto } from './dto/update-domain.dto'

@Injectable()
export class DomainsService {
  constructor(private readonly prisma: PrismaService) {}

  create(createDomainDto: CreateDomainDto) {
    return this.prisma.cmsDomain.create({
      data: {
        ...createDomainDto,
        sites: {},
      },
    })
  }

  findAll(
    params: {
      skip?: number
      take?: number
      cursor?: Prisma.CmsDomainWhereUniqueInput
      where?: Prisma.CmsDomainWhereInput
      orderBy?: Prisma.CmsDomainOrderByWithRelationInput
    } = {},
  ) {
    const { skip, take = 10, cursor, where, orderBy } = params || {}

    const page = skip ? skip / take : 1

    return paginator(
      this.prisma.cmsDomain,
      {
        skip,
        take,
        cursor,
        where,
        orderBy,
      },
      {
        page,
        take,
      },
    )
  }

  findOne(id: string, options?: Record<string, any>) {
    let where = { id }

    if (options) {
      where = { ...where, ...options }
    }

    return this.prisma.cmsDomain.findUnique({
      where,
    })
  }

  update(
    id: string,
    updateDomainDto: UpdateDomainDto,
    options?: Record<string, any>,
  ) {
    let where = { id }
    if (options) {
      where = { ...where, ...options }
    }

    return this.prisma.cmsDomain.update({
      where,
      data: {
        name: updateDomainDto.name,
        domain: updateDomainDto.domain,
        is_active: updateDomainDto.is_active,
        metadata: updateDomainDto.metadata,
        sites: {},
      },
    })
  }

  softDelete(id: string, options?: Record<string, any>) {
    let where = { id }
    if (options) {
      where = { ...where, ...options }
    }

    return this.prisma.cmsDomain.update({
      where,
      data: { deleted_at: new Date() },
    })
  }

  remove(id: string, options?: Record<string, any>) {
    let where = { id }
    if (options) {
      where = { ...where, ...options }
    }

    return this.prisma.cmsDomain.delete({ where })
  }
}
