import { Test, TestingModule } from '@nestjs/testing'
import { DomainsService } from './domains.service'

describe('DomainsService', () => {
  let service: DomainsService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DomainsService],
    }).compile()

    service = module.get<DomainsService>(DomainsService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })
})
