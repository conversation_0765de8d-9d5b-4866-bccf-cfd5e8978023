import { SectionTitle } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { Mail, MapPin, Phone, Printer } from 'lucide-react'

//----------------------------------------------------------------------
const _CONTACT_DETAILS = [
  {
    icon: <MapPin />,
    title: 'Địa chỉ',
    description:
      'Tầng 14 & 16, <PERSON><PERSON><PERSON>, Số 562, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>',
  },
  {
    icon: <Phone />,
    title: 'Hotline',
    description: '1800 599 ************ 6999',
  },
  {
    icon: <Mail />,
    title: 'Email',
    description: '<EMAIL>',
  },
  {
    icon: <Printer />,
    title: 'Fax',
    description: '+84 24 3652 6888',
  },
]
//----------------------------------------------------------------------
export default function ContactDetails() {
  return (
    <div className="flex flex-col gap-y-10 justify-between">
      <div className="flex flex-col gap-6">
        <SectionTitle text="Liên hệ với phú thái cat" align="left" />
        <Typography variant={'body2'}>
          Office ipsum you must be muted. Teeth move hands accountable nail
          picture your company big. Needle nobody keep please what pole. Items
          alpha wiggle wheel info. Panel donuts usabiltiy walk unit individual
          marketing stop window. Disband window we bake me organic diarize.
          Building follow incentivize hands create meat economy contribution
          follow downloaded.
        </Typography>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {_CONTACT_DETAILS.map((item: any, idx: number) => (
          <ContactDetailItem key={idx} item={item} />
        ))}
      </div>
    </div>
  )
}
//----------------------------------------------------------------------
function ContactDetailItem({ item }: { item: any }) {
  return (
    <div
      className="flex flex-col gap-y-4 p-6 bg-gray-50 rounded-xl"
      style={{
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <div
        className=" relative w-[50px] h-[45px] aspect-square flex items-center justify-center bg-[#FFCC00]"
        style={{
          // backgroundImage: 'url(/icons/hexagon-bg.svg)',
          // backgroundSize: 'cover',
          // backgroundPosition: 'center',
          // backgroundRepeat: 'no-repeat',
          clipPath:
            'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
        }}
      >
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          {item.icon}
        </div>
      </div>
      <Typography variant={'body1'} className="font-medium text-gray-600">
        {item.title}
      </Typography>
      <Typography variant={'body2'} className="font-medium text-[#FF9900]">
        {item.description}
      </Typography>
    </div>
  )
}
