import { Typography } from '@ttplatform/ui/components'
import { Mail, Phone } from 'lucide-react'
// ----------------------------------------------------------------------
const _MOCK_DATA = [
  {
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '0123456789',
  },
  {
    name: 'H<PERSON>',
    email: '<EMAIL>',
    phone: '0123456789',
  },
]
// ----------------------------------------------------------------------
export default function HRInfo() {
  return (
    <div
      className="w-full flex flex-col gap-y-6 p-6 bg-gray-50 rounded-lg"
      style={{
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Typography variant={'h6'} className="uppercase text-gray-800">
        THÔNG TIN LIÊN HỆ
      </Typography>
      {_MOCK_DATA.map((item, idx) => (
        <div key={idx} className="flex flex-col gap-y-4">
          <Typography variant={'body2'} className="text-gray-800 font-semibold">
            {item.name}
          </Typography>

          <div className="flex items-center gap-2 flex-wrap">
            <div className="flex items-center gap-2">
              <Mail className="w-5 h-5 min-w-5 text-gray-600" />
              <Typography
                variant={'body2'}
                className="text-gray-600 font-semibold text-wrap"
              >
                Email:
              </Typography>
            </div>
            <Typography
              variant={'body2'}
              className="text-[#FF9900] font-semibold"
            >
              {item.email}
            </Typography>
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            <div className="flex items-center gap-2">
              <Phone className="w-5 h-5 min-w-5 text-gray-600" />
              <Typography
                variant={'body2'}
                className="text-gray-600 font-semibold text-wrap"
              >
                Số điện thoại:
              </Typography>
            </div>
            <Typography
              variant={'body2'}
              className="text-[#FF9900] font-semibold"
            >
              {item.phone}
            </Typography>
          </div>
        </div>
      ))}
    </div>
  )
}
