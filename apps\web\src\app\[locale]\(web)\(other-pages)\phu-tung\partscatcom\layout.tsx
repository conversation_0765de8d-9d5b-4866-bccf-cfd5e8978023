import '@/styles/web.css'

import React from 'react'

export async function generateMetadata() {
  return {
    title: 'Phụ tùng chính hãng CAT | Đặt hàng dễ dàng | PTC',
    description:
      'Đặt mua phụ tùng Cat chính hãng dễ dàng, <PERSON><PERSON><PERSON> chóng và an toàn.',
  }
}

type TProps = {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}

export default async function PartsLayout({ children }: TProps) {
  return <>{children}</>
}
