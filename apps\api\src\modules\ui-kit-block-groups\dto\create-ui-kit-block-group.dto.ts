import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator'
import { UiKitBlock } from '../../ui-kit-blocks/entities/ui-kit-block.entity'

export class CreateUiKitBlockGroupDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsOptional()
  @IsString()
  description: string

  @IsOptional()
  @IsArray()
  group_types: string[]

  @IsOptional()
  @IsNumber()
  rank: number

  @IsArray()
  blocks: UiKitBlock[]

  @IsOptional()
  @IsString()
  created_by: string

  @IsOptional()
  @IsString()
  updated_by: string
}
