{"name": "cms", "private": true, "version": "0.1.0", "description": "CMS v1", "license": "MIT", "scripts": {"dev": "strapi develop --watch-admin --debug", "start": "strapi start", "prebuild": "cd src/plugins/image-point-editor && npm run build", "build": "strapi build", "strapi": "strapi", "deploy": "strapi deploy", "seed": "strapi import -f ./data/export_20250613123509.tar.gz", "postinstall": "node ./scripts/updateUuid.mjs"}, "devDependencies": {"@types/node": "24.0.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.8.3"}, "dependencies": {"@ckeditor/strapi-plugin-ckeditor": "^1.1.1", "@strapi/plugin-cloud": "5.16.0", "@strapi/plugin-color-picker": "^5.16.0", "@strapi/plugin-seo": "^2.0.8", "@strapi/plugin-users-permissions": "5.16.0", "@strapi/strapi": "5.16.0", "patch-package": "^8.0.0", "pg": "^8.16.0", "pluralize": "^8.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-cache": "^1.5.3", "strapi-plugin-navigation": "^3.0.16", "styled-components": "^6.1.18", "uuid": "^11.1.0"}, "strapi": {"uuid": "PTC-340125f8-fd8b-4b06-80a3-c16be79240e3"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}}