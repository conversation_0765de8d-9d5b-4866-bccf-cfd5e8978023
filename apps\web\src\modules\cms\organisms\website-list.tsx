'use client'

import { I_SiteListReturn, I_SiteReturn } from '@ttplatform/common'
import { E_SiteStatus } from '@ttplatform/common/src/libs/enums/cms.enum'
import { <PERSON><PERSON>, Card, Typography } from '@ttplatform/ui/components'
import { EllipsisVerticalIcon, Eye, Pencil } from 'lucide-react'
import Link from 'next/link'
import React from 'react'
import { CustomImage } from '~/src/components'
import { APP_CONFIG } from '~/src/config-global'
import { paths } from '~/src/routes/paths'
import { SiteBadge, SiteLabelStatus } from '../molecules'
import SiteActions from '../molecules/site-actions'
import { SiteDialogForm } from '../molecules/site-dialog-form'

type TProps = {
  sites: I_SiteListReturn
}

export default function WebsiteList({ sites }: TProps) {
  const renderCard = (site: I_SiteReturn) => {
    return (
      <Card className="w-full flex flex-row items-center justify-between p-4">
        <div className="flex items-center gap-3 w-full">
          <div className="w-[160px] h-full rounded-md">
            <CustomImage
              src={site.image || ''}
              alt={site.name}
              className="rounded-md"
              fill
              ratio={1 / 1}
            />
          </div>
          <div className="flex flex-col gap-1 w-full">
            <Typography variant="h4" className="font-bold text-md">
              {site.name}
            </Typography>

            <SiteLabelStatus website={site} />

            <Link
              target="_blank"
              href={`${APP_CONFIG.baseUrl}/${site.id}?mode=preview`}
              rel="noreferrer"
            >
              <Typography
                className="underline"
                variant="body2"
                color="gray-600"
              >
                {APP_CONFIG.baseUrl}/{site.id}
              </Typography>
            </Link>
            <div className="flex gap-2 mt-1">
              <SiteBadge
                text={site.status || ''}
                active={site.status == E_SiteStatus.PUBLISHED}
              />
              <SiteBadge
                text={site.is_primary ? 'Primary' : 'Secondary'}
                active={site.is_primary}
              />
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <SiteActions
            site={site}
            renderTrigger={
              <Button variant="secondary">
                <EllipsisVerticalIcon className="w-4 h-4" />
              </Button>
            }
          />
          <Button variant="outline">
            <Link href={`${paths.app.cms.websites.view(site.id)}`}>
              <Eye className="w-4 h-4" />
            </Link>
          </Button>

          <Button color="info">
            <Link href={`${paths.app.cms.design.edit(site.id)}`}>
              <Pencil className="w-4 h-4" />
            </Link>
          </Button>
        </div>
      </Card>
    )
  }

  const renderHeader = (
    <div className="flex flex-row justify-between w-full">
      <Typography variant="h4">Your Websites</Typography>

      <SiteDialogForm
        title="New Website"
        description="Enter the name of the website you want to create"
        renderTrigger={<Button color="info">+ New Website</Button>}
      />
    </div>
  )

  const renderList = (
    <div className="flex flex-col w-full gap-2">
      {sites.data?.map((site: I_SiteReturn, idx: number) => {
        return (
          <React.Fragment key={idx}>
            {renderCard(site)}

            {sites.metadata.total > 10 ? <p>Add pagination</p> : null}
          </React.Fragment>
        )
      })}
    </div>
  )

  return (
    <>
      {renderHeader}

      {renderList}
    </>
  )
}
