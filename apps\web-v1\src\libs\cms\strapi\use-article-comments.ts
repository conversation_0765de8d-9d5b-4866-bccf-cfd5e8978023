import { APP_CONFIG } from '~/src/config-global'
import { apiRoute, cmsContentType } from '../../constants/cms'
import { QueryParams, useClientFetch } from './use-client-fetch'

const CNT_ARTICLE_COMMENTS = cmsContentType.articleComments

export interface ICommentData {
  name: string
  content: string
  is_guess?: boolean
  article?: string
  parent?: string
}

/**
 * GET LIST ARTICLE COMMENTS
 */
export const useGetArticleComments = ({
  filters,
  locale,
  status,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_ARTICLE_COMMENTS,
    params: {
      filters,
      locale,
      status: status || 'published',
      sort: sort || 'createdAt:desc',
      pagination,
      populate: populate || ['avatar', 'article', 'parent', 'children'],
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

/**
 * CREATE ARTICLE COMMENT
 */
export const createArticleComment = async (commentData: ICommentData) => {
  const response = await fetch(
    `${APP_CONFIG.apiUrl}/api${apiRoute.articleComments}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: commentData,
      }),
    },
  )

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new Error(
      errorData.message || `Comment submission failed: ${response.statusText}`,
    )
  }

  return response.json()
}

/**
 * GET ARTICLE COMMENTS BY ARTICLE ID
 */
export const useGetArticleCommentsByArticleId = ({
  articleId,
  locale,
}: {
  articleId: string
  locale?: string
}) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_ARTICLE_COMMENTS,
    params: {
      locale,
      filters: {
        article: {
          documentId: articleId,
        },
      },
      populate: ['avatar', 'article', 'parent', 'children', 'children.avatar'],
      sort: 'createdAt:asc',
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
