import { IsNotEmpty, <PERSON>O<PERSON>, <PERSON>Optional, IsString } from 'class-validator'

export class CreateUiKitBlockDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  block_type: string

  @IsOptional()
  @IsString()
  brief: string

  @IsOptional()
  @IsString()
  description: string

  @IsOptional()
  @IsString()
  icon: string

  @IsOptional()
  @IsObject()
  metadata: object

  @IsOptional()
  @IsString()
  ui_kit_block_group_id: string

  @IsOptional()
  @IsString()
  created_by: string

  @IsOptional()
  @IsString()
  updated_by: string

  @IsOptional()
  @IsString()
  deleted_at: string

  @IsOptional()
  @IsString()
  created_at: string

  @IsOptional()
  @IsString()
  updated_at: string
}
