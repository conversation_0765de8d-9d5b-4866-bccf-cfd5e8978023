import { PropsWithChildren } from 'react'

import LinguiClientProvider from '@/providers/lingui-client-provider'
import {
  PageLangParam,
  getAllMessages,
  initLingui,
} from '@ttplatform/internationalization'
import { Toaster } from '@ttplatform/ui/components'

export default async function I18nLayout({
  children,
  params,
}: PropsWithChildren<PageLangParam>) {
  const lang = (await params).locale ?? 'en'

  //Server side i18n
  const allMessages = await getAllMessages()
  const messages = allMessages[lang] ?? allMessages['en']
  if (!messages) {
    throw new Error(`No messages found for locale "${lang}" or fallback "en"`)
  }

  const i18n = initLingui(lang)
  i18n.load(lang, messages)
  i18n.activate(lang)

  return (
    <LinguiClientProvider initialLocale={lang} initialMessages={messages}>
      {children}
      <Toaster />
    </LinguiClientProvider>
  )
}
