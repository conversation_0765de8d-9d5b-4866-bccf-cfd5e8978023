{"kind": "collectionType", "collectionName": "solution_applications", "info": {"singularName": "solution-application", "pluralName": "solution-applications", "displayName": "Solution Application"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "name"}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "industrial_solutions": {"type": "relation", "relation": "manyToMany", "target": "api::industrial-solution-category.industrial-solution-category", "mappedBy": "applications"}}}