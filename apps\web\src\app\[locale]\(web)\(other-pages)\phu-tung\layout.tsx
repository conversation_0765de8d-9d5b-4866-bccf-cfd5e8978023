import '@/styles/web.css'

import {
  BCommitment,
  BContactBox,
  BFooterWithSchema,
  BHeaderWithSchema,
} from '@ttplatform/core-page-builder/components'
import React from 'react'

export async function generateMetadata() {
  return {
    title: '<PERSON><PERSON> tùng chính hãng CAT | Đặt hàng dễ dàng | PTC',
    description:
      'Tìm kiếm và đặt mua phụ tùng máy CAT chính hãng nhanh chóng, dễ dàng. Danh mục đầy đủ, hỗ trợ tận tâm từ PTC.',
  }
}

import { _global, _mainNavigation, _topbar } from '../../../../../__mock__'

type TProps = {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}

export default async function PartsLayout({ children, params }: TProps) {
  const { locale } = await params
  return (
    <>
      <BHeaderWithSchema
        header={_global.header as any}
        navigation={_mainNavigation as any}
        locale={locale}
        topbar={_topbar as any}
      />
      {children}
      <BContactBox />
      <BCommitment />
      <BFooterWithSchema data={_global.footer as any} />
    </>
  )
}
