import { Type } from 'class-transformer'
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import { CreateSiteDto } from 'src/modules/sites/dto/create-site.dto'

export class CreateDomainDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  domain: string

  @IsOptional()
  @IsString()
  user_id: string

  @IsOptional()
  @IsBoolean()
  is_active?: boolean

  @IsOptional()
  metadata?: any

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSiteDto)
  sites?: CreateSiteDto[]
}
