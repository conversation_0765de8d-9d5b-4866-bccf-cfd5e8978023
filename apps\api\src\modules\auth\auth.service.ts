import {
  ConflictException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common'
import { ConfigType } from '@nestjs/config'
import { JwtService } from '@nestjs/jwt'
import { hash, verify } from 'argon2'

import { CreateUserDTO } from '../user/dto'
import { UserService } from '../user/user.service'
import refreshConfig from './config/refresh.config'
import { UserLoginDTO } from './dto'

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    @Inject(refreshConfig.KEY)
    private readonly refreshTokenConfig: ConfigType<typeof refreshConfig>,
  ) {}

  async signup(createUserDto: CreateUserDTO) {
    const user = await this.userService.findByEmail(createUserDto.email)
    if (user) throw new ConflictException('User already exists')

    return this.userService.create(createUserDto)
  }

  async validateUser(userLoginDto: UserLoginDTO) {
    const { email, password } = userLoginDto
    const user = await this.userService.findByEmail(email)
    if (!user) throw new UnauthorizedException('User is not found')

    const passwordMatch = await verify(user.password!, password)
    if (!passwordMatch) throw new UnauthorizedException('Password is incorrect')

    return { user }
  }

  async login(userLoginDto: UserLoginDTO) {
    try {
      const { user } = await this.validateUser(userLoginDto)

      const { access_token, refresh_token } = await this.generateToken(user.id)
      const hashedRefreshToken = await hash(refresh_token)
      const hashedAccessToken = await hash(access_token)
      await this.userService.updateRefreshToken(
        user.id,
        hashedAccessToken,
        hashedRefreshToken,
        'local',
      )

      return {
        id: user.id,
        name: user.name,
        access_token,
        refresh_token,
      }
    } catch (error) {
      if (error instanceof HttpException) throw error

      Logger.error('Login failed', error)
      throw new UnauthorizedException('Login failed')
    }
  }

  async generateToken(id: string) {
    const payload = { sub: id }

    const [access_token, refresh_token] = await Promise.all([
      this.jwtService.signAsync(payload),
      this.jwtService.signAsync(payload, this.refreshTokenConfig),
    ])

    return { access_token, refresh_token }
  }

  async validateJwtUser(id: string, access_token: string) {
    const account = await this.userService.findAccount(id, 'local')
    if (!account || !account.access_token || !account.refresh_token)
      throw new UnauthorizedException('Invalid credentials')

    const accessTokenMatch = await verify(account.access_token, access_token)
    const refreshTokenMatch = await verify(account.refresh_token, access_token)

    if (!accessTokenMatch && !refreshTokenMatch) {
      throw new UnauthorizedException('Invalid credentials')
    }

    return account
  }

  async validateRefresh(id: string, refresh_token: string) {
    const account = await this.userService.findAccount(id, 'local')
    if (!account || !account.refresh_token) {
      throw new UnauthorizedException('Invalid credentials')
    }

    const refreshTokenMatch = await verify(account.refresh_token, refresh_token)
    if (!refreshTokenMatch)
      throw new UnauthorizedException('Invalid credentials')

    return account
  }

  async refreshToken(id: string, name?: string) {
    const { access_token, refresh_token } = await this.generateToken(id)
    const hashedRefreshToken = await hash(refresh_token)
    const hashedAccessToken = await hash(access_token)
    await this.userService.updateRefreshToken(
      id,
      hashedAccessToken,
      hashedRefreshToken,
      'local',
    )

    return { id, name, access_token, refresh_token }
  }

  async signout(id: string) {
    await this.userService.updateRefreshToken(id, '', '', 'local')
  }
}
