import { Module } from '@nestjs/common'
import { PrismaService } from 'src/modules/persistence/prisma/prisma.service'
import { UserService } from '../user/user.service'
import { DomainsController } from './domains.controller'
import { DomainsService } from './domains.service'

@Module({
  controllers: [DomainsController],
  providers: [DomainsService, PrismaService, UserService],
})
export class DomainsModule {}
