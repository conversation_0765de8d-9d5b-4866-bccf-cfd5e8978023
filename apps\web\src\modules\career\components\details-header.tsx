import {
  Bread<PERSON>rumb,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON><PERSON>b<PERSON>ink,
  B<PERSON><PERSON><PERSON>b<PERSON>ist,
  B<PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  BreadcrumbSeparator,
  Separator,
  Typography,
} from '@ttplatform/ui/components'
import { Calendar1, MapPin, UserRound } from 'lucide-react'
// ---------------------------------------------------------------------------------
type HeaderProps = {
  heading: any
  BREADCRUMB: {
    title: string
    href: string
  }[]
  currentJob: any
}
// ---------------------------------------------------------------------------------
export default function DetailsHeader({
  heading,
  BREADCRUMB,
  currentJob,
}: Header<PERSON><PERSON>) {
  return (
    <section className="py-10">
      <div className="w-full container max-w-screen-xl mx-auto px-4 flex flex-col gap-10">
        <div className="w-full">
          <Breadcrumb>
            <BreadcrumbList>
              {BREADCRUMB.slice(0, -1).map((item: any, index: number) => (
                <div key={index} className="flex items-center gap-2.5">
                  <BreadcrumbItem>
                    <BreadcrumbLink href={item.href}>
                      {item.title}
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                </div>
              ))}
              <BreadcrumbItem>
                <BreadcrumbPage>
                  {BREADCRUMB[BREADCRUMB.length - 1].title}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="flex flex-col items-center gap-6">
          <Typography
            variant={'h1'}
            className="relative pb-2 before:absolute before:bottom-0  before:content-[''] before:w-[68px] before:h-[8px] before:bg-[#FFCC00] before:left-[50%] before:translate-x-[-50%]"
          >
            {heading.title}
          </Typography>
          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-2">
              <Typography
                variant="body2"
                className="text-[#FF9900] font-medium"
              >
                {currentJob?.workingForm === 'full-time'
                  ? 'Toàn thời gian'
                  : 'Bán thời gian'}
              </Typography>
              <Separator
                orientation="vertical"
                className="bg-red-500 h-[24px]"
              />
              <Typography
                variant="body2"
                className="text-[#FF9900] font-medium"
              >
                {currentJob?.position}
              </Typography>
            </div>
            <div className="flex items-center gap-10">
              <DetailsItem
                icon={<UserRound className="w-5 h-5 min-w-5" />}
                title="Số lượng tuyển"
                description={currentJob?.quantity}
              />
              <DetailsItem
                icon={<Calendar1 className="w-5 h-5 min-w-5" />}
                title="Hạn nộp hồ sơ"
                description={currentJob?.deadline}
              />
              <DetailsItem
                icon={<MapPin className="w-5 h-5 min-w-5" />}
                title="Số lượng tuyển"
                description={currentJob?.location?.name}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
// ---------------------------------------------------------------------------------
function DetailsItem({
  icon,
  title,
  description,
}: {
  icon: any
  title: string
  description: string
}) {
  return (
    <div className="flex items-center gap-1">
      {icon}
      <Typography variant="body2" className="text-gray-600 font-medium">
        {title}:
      </Typography>
      <Typography variant="body2" className="text-[#AF0E0E] font-medium">
        {description}
      </Typography>
    </div>
  )
}
