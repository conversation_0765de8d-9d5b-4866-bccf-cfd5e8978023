'use client'

import { SectionTitle } from '@ttplatform/core-page-builder/components'
import { useCallback, useState } from 'react'
import FilterBar from './components/filter-bar'
import FilterResults from './components/filter-results'

// ---------------------------------------------------------------------------------

const defaultFilters: any = {
  keyword: '',
  category: 'all',
  workingForm: 'full-time',
  location: '',
  sortBy: 'newest',
  show_only_valid_job_postings: true,
}

// ---------------------------------------------------------------------------------

export default function CareerList() {
  const [filters, setFilters] = useState(defaultFilters)

  const handleFilters = useCallback((name: string, value: any) => {
    setFilters((prevState: any) => ({
      ...prevState,
      [name]: value,
    }))
  }, [])

  return (
    <section className="career-list py-10">
      <div className="w-full container max-w-screen-xl mx-auto px-4">
        <div className="flex flex-col gap-10">
          <SectionTitle text="CÁC VỊ TRÍ ĐANG TUYỂN DỤNG" align="center" />
          <div className="grid grid-cols-12 gap-x-5 md:gap-x-10">
            <div className="col-span-12 md:col-span-3">
              <FilterBar filters={filters} onFilters={handleFilters} />
            </div>
            <div className="col-span-12 md:col-span-9">
              <FilterResults filters={filters} onFilters={handleFilters} />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
