import { PLUGIN_ID } from './pluginId'
import { prefixPluginTranslations } from './utils/prefixPluginTranslations'

export default {
  register(app: any) {
    app.customFields.register({
      name: PLUGIN_ID,
      type: 'json',
      pluginId: PLUGIN_ID,
      intlLabel: {
        id: `${PLUGIN_ID}.plugin.name`,
        defaultMessage: 'Image Point Editor',
      },
      intlDescription: {
        id: `${PLUGIN_ID}.plugin.description`,
        defaultMessage: 'Easy to use image point editor for Strapi',
      },
      components: {
        Input: async () =>
          import('./components/PointerEditor').then((module) => ({
            default: module.CustomField,
          })),
      },
    })
  },

  async registerTrads({ locales }: { locales: string[] }) {
    const importedTrads = await Promise.all(
      locales.map((locale) => {
        return import(`./translations/${locale}.json`)
          .then(({ default: data }) => {
            return {
              data: prefixPluginTranslations(data, PLUGIN_ID),
              locale,
            }
          })
          .catch(() => {
            return {
              data: {},
              locale,
            }
          })
      }),
    )

    return Promise.resolve(importedTrads)
  },
}
