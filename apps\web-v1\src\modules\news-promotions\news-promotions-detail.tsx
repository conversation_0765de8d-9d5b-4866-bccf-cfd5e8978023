'use client'
import { t } from '@lingui/core/macro'
import {
  CmsMedia,
  LayoutContainer,
  MainButton,
} from '@ttplatform/core-page-builder/components'
import {
  ECardType,
  TArticleSchema,
  TPromotionSchema,
} from '@ttplatform/core-page-builder/libs'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Typography,
} from '@ttplatform/ui/components'
import { format } from 'date-fns'
import { Calendar1, CalendarIcon, ChevronDownIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useCallback, useEffect, useRef, useState } from 'react'
import {
  LatestPromotions,
  PageHeroSection,
  PromotionProductsSection,
} from '~/src/components/dynamic-zone'
import { RenderImageUrlStrapi } from '~/src/components/render-imageUrl-strapi'
import BlockEditor from '~/src/components/renderer/block-editor'
import { APP_CONFIG, KEYS } from '~/src/config-global'
import { useGetArticles } from '~/src/libs/cms/strapi/use-articles'
import { useGetPromotions } from '~/src/libs/cms/strapi/use-promotions'
import { apiRoute } from '~/src/libs/constants/cms'
import { useLocale } from '~/src/libs/data/use-locale'
import { cn } from '~/src/utils'
import { fDate } from '~/src/utils/date-format'
import {
  NewsCommentSection,
  NewsPromotionsImage,
  NewsPromotionsImagesCompare,
  PostCompactList,
} from './components'

type TProps = {
  data: TArticleSchema | TPromotionSchema | null
}

const componentMapping: { [key: string]: any } = {
  'dynamic-zone.related-products': PromotionProductsSection,
  'dynamic-zone.page-hero-section': PageHeroSection,
  'elementals.editor': BlockEditor,
  'elementals.media-video': null,
  'elementals.media-image': NewsPromotionsImage,
  'elementals.compare-images': NewsPromotionsImagesCompare,
  'dynamic-zone.latest-promotions-section': LatestPromotions,
}

const { keyViewed } = KEYS

const NewsPromotionsDetail = ({ data }: TProps) => {
  const [nestedHeadings, setNestedHeadings] = useState<any[]>([])
  const imageRef = useRef<HTMLImageElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const [imageHeight, setImageHeight] = useState(0)
  const pathname = usePathname()
  const locale = useLocale()
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    // Wait for content to render, then extract headings from BlockEditor content only
    const timer = setTimeout(() => {
      if (contentRef.current) {
        const headingElements = Array.from(
          contentRef.current.querySelectorAll('h2, h3, h4, h5, h6'),
        )

        // Auto-generate data-id for headings that don't have them
        headingElements.forEach((heading: any, index) => {
          if (!heading.getAttribute('data-id')) {
            // Generate ID from heading text, making it URL-safe
            const text = heading.innerText || heading.textContent || ''
            const id =
              text
                .toLowerCase()
                .trim()
                .replace(/[^\w\s-]/g, '') // Remove special characters
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/-+/g, '-') // Replace multiple hyphens with single
                .replace(/^-|-$/g, '') || // Remove leading/trailing hyphens
              `heading-${index}` // Fallback ID

            heading.setAttribute('data-id', id)
          }
        })

        const nestedHeadings = getNestedHeading(headingElements)
        setNestedHeadings(nestedHeadings)
      }
    }, 100)

    return () => clearTimeout(timer)
  }, [data?.dynamic_zone, data?.content])

  const updateView = useCallback(async () => {
    const storageKey = `${keyViewed}_${data?.documentId}`

    if (localStorage.getItem(storageKey)) {
      return
    }

    try {
      const newViewCount = (data?.view || 0) + 1

      await fetch(
        `${APP_CONFIG.apiUrl}/api${apiRoute.articles}/${data?.documentId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ data: { view: newViewCount } }),
        },
      )

      localStorage.setItem(storageKey, newViewCount.toString())
    } catch (error) {
      console.error('Failed to update view count:', error)
    }
  }, [data?.documentId, data?.view])

  useEffect(() => {
    updateView()
  }, [updateView])
  const {
    data: latestNews,
    isLoading: isLoadingLatestNews,
    error: errorLatestNews,
  } = useGetArticles({
    locale,

    pagination: {
      limit: 6,
      start: 0,
    },
    sort: 'createdAt:desc',
  })

  const {
    data: latestPromotions,
    isLoading: isLoadingLatestPromotions,
    error: errorLatestPromotions,
  } = useGetPromotions({
    locale,

    pagination: {
      limit: 6,
      start: 0,
    },
    sort: 'createdAt:desc',
  })

  const renderHeader = (
    <div className="flex flex-col gap-y-4">
      {data?.title && (
        <div>
          <Typography
            variant="h1"
            className="uppercase text-center text-[#182230]"
          >
            {data?.title}
          </Typography>
          <div className="w-[68px] mx-auto h-[8px] rounded-none  bg-gradient-to-r from-[#FFCC00] via-[#FFD326] to-[#FFDE59] pt-2" />
        </div>
      )}
      {data?.description && (
        <Typography
          variant="body1"
          className=" text-center text-[#344054] leading-[31px]"
          weight="normal"
        >
          {data?.description}
        </Typography>
      )}
    </div>
  )

  const renderDynamicZone = (
    <>
      {data?.dynamic_zone?.map((componentData: any, key: number) => {
        try {
          const Component = componentMapping[componentData?.__component]

          if (!Component) {
            console.warn(`No component found for: ${componentData.__component}`)
            return null
          }
          const props =
            componentData.__component === 'elementals.editor'
              ? { content: componentData.editor, locale: data?.locale }
              : componentData.__component ===
                  'dynamic-zone.latest-promotions-section'
                ? { ...componentData, promotions: latestPromotions?.data }
                : { ...componentData, locale: data?.locale }

          return <Component key={key} {...props} />
        } catch (error) {
          console.error(
            `Error rendering component: ${componentData.__component}`,
            error,
          )
          return null
        }
      })}
    </>
  )
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (imageRef.current) {
      setImageHeight(imageRef.current.clientHeight)
    }
  }, [imageRef.current])
  const renderContentPromotion = (
    <div className="flex flex-col">
      <div className="grid grid-col-1 lg:grid-cols-2 pt-16">
        <Image
          ref={imageRef}
          src={RenderImageUrlStrapi(data?.image as any) || ''}
          alt={''}
          width={1000}
          height={1000}
          className="w-full h-full object-cover"
        />
        <div
          className="bg-[#FFFBED] p-10 flex flex-col gap-y-6 overflow-y-auto"
          style={{ height: imageHeight }}
        >
          <BlockEditor content={data?.content as any} />
          <div className="flex items-center gap-x-2">
            <Calendar1 className="w-4 h-4 text-[#475467]" />
            <Typography
              variant={'body2'}
              className="text-[#475467]"
              weight={'medium'}
            >
              {t`Valid until`}
            </Typography>
            <Typography
              variant="body2"
              className="text-[#AF0E0E]"
              weight={'bold'}
            >
              {fDate(data?.valid_until || '')}
            </Typography>
          </div>
          <MainButton
            label={t`Mua Ngay`}
            variant="primary"
            url="/"
            target="_blank"
          />
        </div>
      </div>
      {renderDynamicZone}
    </div>
  )

  const handleSmoothScrollToHeading = (e: React.MouseEvent, id: string) => {
    e.preventDefault()
    const element = document.querySelector(`[data-id="${id}"]`)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })

      window.history.pushState(null, '', `#${id}`)
    }
  }

  const renderTOC = (
    <div className="w-full space-y-6">
      {nestedHeadings.length > 0 && (
        <div className="bg-gray-50 rounded-xl p-6 xl:p-10 shadow-md">
          <Accordion type="single" defaultValue="item-1" collapsible>
            <AccordionItem value="item-1">
              <AccordionTrigger
                icon={<ChevronDownIcon className="size-6" />}
                className="flex justify-between items-center w-full text-2xl font-bold text-gray-800 pt-0 rounded-none hover:no-underline border-b"
              >
                <Typography
                  variant="h4"
                  weight="bold"
                  className="text-gray-800 uppercase"
                >
                  {t`Mục lục`}
                </Typography>
              </AccordionTrigger>
              <AccordionContent className="mt-6">
                <div className="font-medium space-y-4 sm:space-y-6 text-gray-700">
                  {nestedHeadings.map((item, index) => (
                    <div key={index}>
                      <Typography
                        variant="body1"
                        className="text-gray-700 cursor-pointer hover:text-primary-accent w-fit"
                      >
                        <Link
                          href={`#${item.id}`}
                          onClick={(e) =>
                            handleSmoothScrollToHeading(e, item.id)
                          }
                        >
                          {' '}
                          {`${index + 1}.`} {item.title}
                        </Link>
                      </Typography>
                      {item.items && item.items.length > 0 && (
                        <div className="list-decimal pl-8 mt-3 space-y-2 sm:space-y-4">
                          {item.items.map((subItem: any, indexItem: number) => (
                            <div key={indexItem}>
                              <Typography
                                variant="body1"
                                className="text-gray-700 cursor-pointer hover:text-primary-accent w-fit"
                              >
                                <Link
                                  href={`#${subItem.id}`}
                                  onClick={(e) =>
                                    handleSmoothScrollToHeading(e, subItem.id)
                                  }
                                >
                                  {' '}
                                  {`${index + 1}.${indexItem + 1}.`}{' '}
                                  {subItem.title}
                                </Link>
                              </Typography>
                              {subItem.items && subItem.items.length > 0 && (
                                <div className="list-decimal pl-9 mt-3 space-y-2 sm:space-y-4">
                                  {subItem.items.map(
                                    (subSubItem: any, indexSubItem: number) => (
                                      <Typography
                                        key={indexSubItem}
                                        variant="body1"
                                        className="text-gray-700 cursor-pointer hover:text-blue-600 w-fit"
                                      >
                                        <Link
                                          href={`#${subSubItem.id}`}
                                          onClick={(e) =>
                                            handleSmoothScrollToHeading(
                                              e,
                                              subSubItem.id,
                                            )
                                          }
                                        >
                                          {' '}
                                          {`${index + 1}.${indexItem + 1}.${indexSubItem + 1}.`}{' '}
                                          {subSubItem.title}
                                        </Link>
                                      </Typography>
                                    ),
                                  )}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      )}
    </div>
  )

  const renderContentArticle = (
    <div className="flex flex-col gap-y-10">
      <CmsMedia
        media={data?.image}
        className="w-full h-full !object-cover "
        contentClassName="w-full h-full rounded-3xl overflow-hidden"
        width={1000}
        height={1000}
      />
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-10">
        <div className="lg:col-span-8 flex flex-col gap-y-6">
          {renderTOC}
          <div className="flex flex-col gap-y-10" ref={contentRef}>
            {renderDynamicZone}
          </div>
        </div>
        <div className="lg:col-span-4 flex flex-col gap-10">
          <PostCompactList
            headerTitle={t`bài viết mới nhất`}
            postList={latestNews?.data || []}
            type={ECardType.News}
            isLoading={isLoadingLatestNews}
            error={errorLatestNews}
          />
          <PostCompactList
            headerTitle={t`khuyến mại mới nhất`}
            postList={latestPromotions?.data || []}
            type={ECardType.Promotion}
            isLoading={isLoadingLatestPromotions}
            error={errorLatestPromotions}
          />
        </div>
      </div>
    </div>
  )
  return (
    <LayoutContainer className={cn(pathname.includes('promotions') && 'pt-0')}>
      <div className="w-full flex justify-between items-center mb-10">
        {pathname.includes('news') && (
          <>
            <Typography
              variant={'body1'}
              weight={'bold'}
              className="text-[#FF9900]"
            >
              {data?.category?.title}
            </Typography>
            <div className="flex items-center text-red-700">
              <CalendarIcon className="mr-1" />
              <Typography variant="caption">
                {data?.createdAt
                  ? format(new Date(data?.createdAt), 'dd MMMM yyyy')
                  : ''}
              </Typography>
            </div>
          </>
        )}
      </div>
      {(data?.title || data?.description) && (
        <div className="pb-5">{renderHeader}</div>
      )}

      {pathname.includes('promotions') ? (
        renderContentPromotion
      ) : (
        <div className="flex flex-col gap-y-10">
          {renderContentArticle}
          <NewsCommentSection
            initialComments={data?.article_comments || []}
            articleId={data?.documentId}
          />
        </div>
      )}
    </LayoutContainer>
  )
}

function getNestedHeading(headingElements: any) {
  const nestedHeadings: any[] = []
  const stack: any[] = []

  headingElements.forEach((heading: any) => {
    const { innerText: title } = heading
    const id = heading.getAttribute('data-id')
    const level = Number.parseInt(heading.nodeName.substring(1))

    if (!title || title.trim() === '') {
      return
    }

    const newItem = { id, title, items: [], level }

    if (level === 2) {
      nestedHeadings.push(newItem)
      stack.length = 0
      stack.push(newItem)
    } else if (level > 2) {
      while (stack.length > 0 && stack[stack.length - 1].level >= level) {
        stack.pop()
      }

      if (stack.length > 0) {
        const parent = stack[stack.length - 1]
        parent.items.push(newItem)
        stack.push(newItem)
      } else {
        nestedHeadings.push(newItem)
        stack.push(newItem)
      }
    }
  })

  const cleanItems = (items: any[]): any[] => {
    return items.map((item) => ({
      id: item.id,
      title: item.title,
      items: cleanItems(item.items),
    }))
  }

  return cleanItems(nestedHeadings)
}

export default NewsPromotionsDetail
