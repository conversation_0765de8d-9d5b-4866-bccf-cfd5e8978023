{"collectionName": "components_dynamic_zone_solution_portfolio_sections", "info": {"displayName": "Solution_Portfolio_Section"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "items": {"type": "relation", "relation": "oneToMany", "target": "api::industrial-solution.industrial-solution"}}, "config": {}}