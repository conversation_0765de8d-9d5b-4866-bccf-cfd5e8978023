'use client'

import { But<PERSON> } from '@ttplatform/ui/components'
import { useState } from 'react'

interface CommentInputProps {
  onSubmit: (text: string, name: string) => void
  placeholder?: string
  buttonText?: string
  isReply?: boolean
}

const CommentInput = ({
  onSubmit,
  placeholder = 'Viết bình luận...',
  buttonText = 'BÌNH LUẬN',
  isReply = false,
}: CommentInputProps) => {
  const [text, setText] = useState('')
  const [name, setName] = useState('')

  const handleSubmit = () => {
    if (text.trim() && name.trim()) {
      onSubmit(text, name)
      setText('')
      setName('')
    }
  }

  return (
    <div className="flex flex-col gap-3">
      <input
        type="text"
        value={name}
        onChange={(e) => setName(e.target.value)}
        placeholder="Tên của bạn"
        className="w-full px-4 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
      />
      <div className="flex gap-3">
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder={placeholder}
          rows={2}
          className="flex-1 px-4 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 resize-none"
        />
        <Button
          onClick={handleSubmit}
          disabled={!text.trim() || !name.trim()}
          className="bg-yellow-400 hover:bg-yellow-500 text-black font-semibold h-auto whitespace-nowrap"
        >
          {buttonText}
        </Button>
      </div>
    </div>
  )
}

export default CommentInput
