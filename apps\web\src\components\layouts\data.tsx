import { paths } from '@/routes/paths'

import {
  BookOpen,
  BotIcon,
  CodeSquare,
  Globe,
  Home,
  Import,
  PlugIcon,
  Settings2,
  SquareTerminal,
  User2,
} from 'lucide-react'
import { AppLogo } from '../logo'

// This is sample data.
export const data = {
  teams: [
    {
      name: 'AFFION',
      expandedLogo: <AppLogo className="h-full w-auto" />,
      collapsedLogo: <AppLogo className="h-full w-auto" />,
      plan: 'Loyalty Platform',
    },
  ],
  navMain: [
    // DASHBOARD
    {
      title: 'Dashboard',
      icon: <Home />,
      url: paths.app.root,
    },
    // MEMBERS
    {
      title: 'CMS',
      url: paths.app.cms.root,
      icon: <CodeSquare />,
      items: [
        {
          title: 'Websites',
          url: paths.app.cms.websites.root,
          icon: <Globe />,
        },
        {
          title: 'Posts',
          url: paths.app.cms.posts.root,
        },
      ],
    },
    // MEMBERS
    {
      title: 'Members',
      url: paths.app.members.root,
      icon: <User2 />,
      items: [
        {
          title: 'List',
          url: paths.app.members.root,
        },
        {
          title: 'New',
          url: paths.app.members.new,
        },
      ],
    },

    // GLOBAL MANAGEMENT
    {
      title: 'Global Management',
      url: paths.app.globalManagement.root,
      icon: <SquareTerminal />,
      isActive: true,
      items: [
        {
          title: 'Analytics',
          url: paths.app.globalManagement.analytics,
        },
        {
          title: 'Settings',
          url: paths.app.globalManagement.settings,
        },
        {
          title: 'Config Duplication',
          url: paths.app.globalManagement.configDuplication,
        },
        {
          title: 'Usages',
          url: paths.app.globalManagement.usages,
        },
      ],
    },
    // SETTINGS
    {
      title: 'Settings',
      url: paths.app.settings.root,
      icon: <Settings2 />,
      items: [
        {
          title: 'Profile',
          url: paths.app.settings.profile,
        },
        {
          title: 'Security',
          url: paths.app.settings.security,
        },
        {
          title: 'Notifications',
          url: paths.app.settings.notifications,
        },
        {
          title: 'Subscriptions',
          url: paths.app.settings.subscriptions,
        },
        {
          title: 'Translations',
          url: paths.app.settings.translations,
        },
      ],
    },
  ],
  integration: [
    {
      title: 'Webhooks',
      url: paths.app.integration.webhooks,
      icon: <PlugIcon />,
    },
    {
      title: 'Import / Export',
      url: paths.app.integration.importExport,
      icon: <Import />,
    },
  ],
  helper: [
    {
      title: 'Docs',
      url: paths.app.helper.docs,
      icon: <BookOpen />,
    },
    {
      title: 'Support',
      url: paths.app.helper.support,
      icon: <BotIcon />,
    },
  ],
}
