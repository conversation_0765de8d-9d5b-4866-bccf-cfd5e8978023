import { SectionTitle } from '@ttplatform/core-page-builder/components'
import { CardProduct } from '../../../../../../packages/core/page-builder/src/components/ui/cards/card-product'

//---------------------------------------------------------------------------------
const __LIST = [
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
]
//---------------------------------------------------------------------------------
type IProps = {
  product: any
}
//---------------------------------------------------------------------------------
export function DetailsCompare({ product }: IProps) {
  return (
    <section id="SO_SANH" className="relative bg-white z-10">
      <div className="relative z-30 w-full container max-w-screen-xl mx-auto px-4">
        <div className="flex flex-col gap-10 py-10 lg:py-20">
          <SectionTitle
            text={`so sánh CAT® ${product?.product_code} với các sản phẩm tương tự`}
            align="left"
          />
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-4">
            {__LIST?.map((product: any, idx: number) => (
              <CardProduct key={idx} product={product} />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
