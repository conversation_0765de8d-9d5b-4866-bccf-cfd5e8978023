import { <PERSON><PERSON><PERSON>out, PageStatus } from '@prisma/client'
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class CreatePageDto {
  @IsNotEmpty()
  @IsString()
  title: string

  @IsNotEmpty()
  @IsString()
  slug: string

  @IsString()
  site_id: string

  @IsOptional()
  content?: any

  @IsOptional()
  metadata?: any

  @IsOptional()
  @IsBoolean()
  is_default?: boolean

  @IsOptional()
  @IsBoolean()
  is_hide_nav?: boolean

  @IsOptional()
  @IsBoolean()
  is_hide_footer?: boolean

  @IsOptional()
  @IsBoolean()
  is_hide_header?: boolean

  @IsOptional()
  @IsBoolean()
  is_hide_sidebar?: boolean

  @IsOptional()
  status?: PageStatus

  @IsOptional()
  page_layout?: PageLayout

  @IsOptional()
  @IsString()
  created_by?: string
}
